# 营销管理系统后端

## 技术栈
- Spring Boot 3.x
- Java 17+
- Maven 3.8+
- MySQL 8.0+
- MyBatis Plus
- Redis

## 项目结构
```
backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── marketing/
│   │   │           └── management/
│   │   │               ├── MarketingManagementApplication.java
│   │   │               ├── config/
│   │   │               ├── controller/
│   │   │               ├── service/
│   │   │               ├── mapper/
│   │   │               ├── entity/
│   │   │               └── dto/
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       ├── application-prod.yml
│   │       └── mapper/
│   └── test/
├── pom.xml
└── README.md
```

## 开发环境搭建

### 1. 环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库配置
```sql
CREATE DATABASE marketing_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 启动项目
```bash
cd backend
mvn spring-boot:run
```

## API文档
项目启动后访问: http://localhost:8080/swagger-ui.html

## 主要功能模块

### 1. 分支机构分类评级管理
- 分支机构评级设置
- 评级历史记录查询
- 评级数据导入导出

### 2. 用户管理
- 用户登录认证
- 权限管理
- 用户信息维护

### 3. 系统管理
- 系统配置
- 日志管理
- 数据字典

## 开发规范

### 1. 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 必须编写单元测试

### 2. 接口规范
- RESTful API设计
- 统一的响应格式
- 完整的接口文档

### 3. 数据库规范
- 统一的命名规范
- 必要的索引设计
- 数据库版本管理
