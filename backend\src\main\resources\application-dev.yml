# 开发环境配置
spring:
  datasource:
    url: *****************************************************************************************************************************************************************
    username: root
    password: 123456
    
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0

# 日志配置
logging:
  level:
    root: info
    com.marketing.management: debug
  file:
    name: logs/marketing-management-dev.log

# 开发环境允许跨域
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
