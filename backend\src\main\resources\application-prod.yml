# 生产环境配置
spring:
  datasource:
    url: ******************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0

# 日志配置
logging:
  level:
    root: warn
    com.marketing.management: info
  file:
    name: logs/marketing-management-prod.log

# 生产环境安全配置
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: never
