name: CI

on:
  push:
    branches:
      - main

jobs:
  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          run_install: true

      - name: Use Node.js LTS
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          registry-url: https://registry.npmjs.org/
          cache: pnpm

      - name: Type Check
        run: npm run typecheck
