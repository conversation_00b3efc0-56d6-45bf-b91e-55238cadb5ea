# frontend 项目目录结构说明

## 核心目录
1. **`src/`**  
   - 主开发目录，包含所有前端源码和资源

2. **`src/components/`**  
   - 存放全局可复用组件
   - 关键组件：
     - `BaseHeader.vue`：顶部导航栏组件
     - `BaseSide.vue`：侧边栏导航组件
     - `MessageBoxDemo.vue`：消息弹窗示例组件

3. **`src/composables/`**  
   - 存放可组合逻辑函数
   - 示例：
     - `useDarkMode.ts`：暗黑模式切换逻辑
     - `useFetch.ts`：数据请求封装

4. **`src/pages/`**  
   - 页面级组件，按功能模块划分
   - 示例结构：
     - `Home/`：首页相关
     - `User/`：用户管理
     - `Settings/`：系统设置

5. **`src/styles/`**  
   - 全局样式和主题配置
   - 重要文件：
     - `element/index.scss`：Element Plus 主题定制
     - `variables.scss`：SCSS 变量定义
     - `unocss.css`：UnoCSS 生成的实用类

## 配置文件
1. **`vite.config.ts`**  
   - Vite 构建配置
   - 关键配置项：
     - 插件：`unplugin-vue-components`（组件自动导入）
     - 别名：`@ → src/`
     - CSS 预处理器配置

2. **`uno.config.ts`**  
   - UnoCSS 原子化 CSS 配置
   - 包含预设规则和自定义规则

3. **`tsconfig.json`**  
   - TypeScript 类型配置（注：项目已切换为 JS）

4. **`.eslintrc`**  
   - 代码规范配置（基于 @antfu/eslint-config）

## 入口文件
1. **`main.js`**  
   - 应用主入口
   - 功能：
     - 初始化 Vue 应用
     - 注册全局插件（路由、状态管理等）
     - 挂载根组件

2. **`App.vue`**  
   - 根组件
   - 包含：
     - 基础布局结构
     - 路由视图容器 (`<router-view>`)

## 其他重要目录
1. **`public/`**  
   - 静态资源（直接复制到构建输出）
   - 包含 favicon、全局 JS/CSS 等

2. **`dist/`**  
   - 构建输出目录（通过 `npm run build` 生成）

3. **`node_modules/`**  
   - 依赖库目录（由 pnpm/npm 自动管理）