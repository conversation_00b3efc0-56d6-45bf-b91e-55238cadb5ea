{"name": "marketing-management-frontend", "type": "module", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.15.0", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build:test": "cross-env NODE_ENV=test vite build", "build:prod": "cross-env NODE_ENV=production vite build", "build:dev": "cross-env NODE_ENV=development vite build", "generate": "vite-ssg build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.0.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "docx-preview": "^0.3.5", "echarts": "^5.6.0", "element-plus": "^2.9.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify-json/ep": "^1.2.1", "@iconify-json/ri": "^1.2.3", "@unocss/eslint-plugin": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "cross-env": "^10.0.0", "eslint": "^9.16.0", "eslint-plugin-format": "^0.1.3", "eslint-plugin-vue": "^9.30.0", "sass": "^1.82.0", "unocss": "^0.65.1", "unplugin-vue-components": "^0.27.5", "unplugin-vue-router": "^0.10.9", "vite": "^6.0.3", "vite-ssg": "^0.24.2"}}