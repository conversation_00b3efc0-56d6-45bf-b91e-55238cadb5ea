<template>
  <el-config-provider namespace="ep">
    <!-- 独立页面：不显示导航栏和侧边栏 -->
    <div v-if="isStandalonePage" class="standalone-page">
      <BaseHeader />
      <RouterView />
    </div>
    <!-- 普通页面：显示完整布局 -->
    <div v-else>
      <BaseHeader />
      <div class="main-container flex">
        <BaseSide />
        <div w="full" py="4">
          <RouterView />
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import { computed, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute();
const router = useRouter();

// 定义需要独立显示的页面路径
const standalonePages = [
  // 基础页面
  // "/",
  "/login",

  // // branch-kpi 主页面
  // "/branch-kpi/dashboard",
  // "/branch-kpi/assessment-dashboard",
  // "/branch-kpi/branch-kpi-completion-status",
  // "/branch-kpi/system-data-audit",
  // "/branch-kpi/import-audt",

  // // branch-kpi/param-conf 参数配置
  // "/branch-kpi/param-conf/param-weight",
  // "/branch-kpi/param-conf/branch-rating-setting",
  // "/branch-kpi/param-conf/business-equity-conversion-setting",
  // "/branch-kpi/param-conf/ib-workload-weight-setting",
  // "/branch-kpi/param-conf/bonus-provision-ratio",
  // "/branch-kpi/param-conf/ib-serv-prsn-mtc",
  // "/branch-kpi/param-conf/branch-ib-integration-relation-maintenance",
  // "/branch-kpi/param-conf/employee-qualification-exam-maintenance",
  // "/branch-kpi/param-conf/assessment-related-regulation-maintenance",

  // // branch-kpi/kpi-items 指标项目
  // "/branch-kpi/kpi-items/assessment-related-regulation-query",
  // "/branch-kpi/kpi-items/exam-financial-report",
  // "/branch-kpi/kpi-items/exam-financial-report-branch",
  // "/branch-kpi/kpi-items/employee-qualification-exam-statistics",
  // "/branch-kpi/kpi-items/other-add-subtract-points",
  // "/branch-kpi/kpi-items/customer-data-statistics",
  // "/branch-kpi/kpi-items/new-valid-customer-statistics",
  // "/branch-kpi/kpi-items/ib-workload-score",
  // "/branch-kpi/kpi-items/ib-business-achievement-statistics",
  // "/branch-kpi/kpi-items/ib-workload-statistics",
  // "/branch-kpi/kpi-items/wealth-management-statistics",
  // "/branch-kpi/kpi-items/risk-management-statistics",
  // "/branch-kpi/kpi-items/employee_info_statistics",
  // "/branch-kpi/kpi-items/team_construction_score",
  // "/branch-kpi/kpi-items/market_share_stats",
  // "/branch-kpi/kpi-items/initial-financial-report",

  // // branch-kpi/manual-entry 手工录入
  // "/branch-kpi/manual-entry/branch-exam-target-entry",
  // "/branch-kpi/manual-entry/financial-data-entry",
  // "/branch-kpi/manual-entry/financial-exam-entry",
  // "/branch-kpi/manual-entry/financial-manage-entry",
  // "/branch-kpi/manual-entry/risk-manage-entry",
  // "/branch-kpi/manual-entry/exchange-proj-entry",
  // "/branch-kpi/manual-entry/ib-word-situ",
  // "/branch-kpi/manual-entry/ib-busi-aet",
  // "/branch-kpi/manual-entry/mkt-prsn-ior",
  // "/branch-kpi/manual-entry/prsn-vol-scor",
  // "/branch-kpi/manual-entry/mkt-mtch",
  // "/branch-kpi/manual-entry/oth-aasp",

  // // branch-kpi/system-data-audits 系统数据审计
  // "/branch-kpi/system-data-audits/ib-business-income-report",
  // "/branch-kpi/system-data-audits/employee-info-query",
  // "/branch-kpi/system-data-audits/capital-statement-daily",
  // "/branch-kpi/system-data-audits/business-person-relation-query",
  // "/branch-kpi/system-data-audits/retired-employee-info-query",
  // "/branch-kpi/system-data-audits/simple-employee-info-query",
  // "/branch-kpi/system-data-audits/employee-info-all-query",

  // // branch-kpi/assessment-scoring-accounting 考核评分核算
  // "/branch-kpi/assessment-scoring-accounting/branch-assessment-score",
  // "/branch-kpi/assessment-scoring-accounting/branch-assessment-score-detail",

  // // branch-kpi/data-check-cal 数据检查计算
  // "/branch-kpi/data-check-cal/basic-data-preparation",
  // "/branch-kpi/data-check-cal/etl-task",
  // "/branch-kpi/data-check-cal/etl-task-his",

  // // branch-performance 分支机构绩效
  // "/branch-performance/quarter-performance",
  // "/branch-performance/quarter-performance-center",
  // "/branch-performance/ib-quarter-performance",
  // "/branch-performance/ib-quarter-performance-center",

  // // branch-performance/quarterly-bonus-accounting 季度奖金核算
  // "/branch-performance/quarterly-bonus-accounting/branch-monthly-profit-statistics",
  // "/branch-performance/quarterly-bonus-accounting/ib-fusion-performance-bonus",
  // "/branch-performance/quarterly-bonus-accounting/branch-quarterly-bonus-accounting",

  // // branch-performance/data-check-cal 数据检查计算
  // "/branch-performance/data-check-cal/kpi-etl-task",

  // // employee-kpi 员工KPI
  // "/employee-kpi/manual-entry/oth-scor",

  // // employee-kpi/param-conf 员工KPI参数配置
  // "/employee-kpi/param-conf/equity-weight-maintenance",
  // "/employee-kpi/param-conf/market-dept-manager-maintenance",
  // "/employee-kpi/param-conf/marketing-personnel-level-grade-maintenance",
  // "/employee-kpi/param-conf/marketing-personnel-optional-index-setting",
  // "/employee-kpi/param-conf/marketing-personnel-required-index-lower-limit-maintenance",
  // "/employee-kpi/param-conf/marketing-personnel-required-index-setting",

  // "/employee-kpi/data-check-cal/employee-kpi-task",
  // "/employee-kpi/employee-import-audt",
  // // employee-kpi/kpi-items 考核指标得分明细查询
  // "/employee-kpi/kpi-items/busi-query/busi_equi_exam_scor_dtl"

];

// 判断当前页面是否为独立页面
const isStandalonePage = computed(() => {
  return standalonePages.includes(route.path);
});

// 在开发和测试环境默认跳转到/login
onMounted(() => {
  const env = process.env.NODE_ENV;
  // 如果是根路径且处于开发或测试环境，则跳转到/login
  if (route.path === '/' && (env === 'development' || env === 'test')) {
    router.push('/login');
  }
});
</script>

<style>
#app {
  text-align: center;
  color: var(--ep-text-color-primary);
}

.main-container {
  height: calc(100vh - var(--ep-menu-item-height) - 4px);
}

.standalone-page {
  width: 100%;
  height: 100vh;
  overflow: auto;
}
</style>