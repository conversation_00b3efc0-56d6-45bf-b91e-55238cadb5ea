<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="username">
      <el-input v-model="loginForm.username" placeholder="请输入用户名 (admin)">
        <template #prefix>
          <el-icon class="el-input__icon">
            <user />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password" style="margin-bottom: 0">
      <el-input
        v-model="loginForm.password"
        type="password"
        placeholder="请输入密码 (admin)"
        show-password
        autocomplete="new-password">
        <template #prefix>
          <el-icon class="el-input__icon">
            <lock />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>    
  </el-form>
  <div class="login-btn">
    <!-- <el-button :icon="CircleClose" round size="large" @click="resetForm"> 重置 </el-button> -->
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login"> 登录 </el-button>
  </div>
  
  <!-- <SliderCaptcha ref="captchaRef" @success="onSliderSuccess" @close="onCaptchaClose" /> -->
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

import { CircleClose, Lock, User, UserFilled } from '@element-plus/icons-vue';


import { onMounted, reactive, ref } from 'vue';
import { ElNotification } from 'element-plus';
import IconInput  from '~/components/comm/IconInput.vue';
import http from '~//utils/http'

const router = useRouter();


const loginFormRef = ref();
const loginRules = reactive({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
});

const loading = ref(false);
const loginForm = reactive({
  username: 'admin',
  password: 'admin',
  // clientId: '',
  // grantType: ''
});

const onSliderSuccess = async () => {
  await performLogin();
};

const performLogin = async () => {
  loading.value = true;
  try {
    // 简单的本地验证，默认用户名和密码都是 admin
    if (loginForm.username === 'admin' && loginForm.password === 'admin') {
      // 保存登录状态到 localStorage
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('username', loginForm.username);
      localStorage.setItem('userToken', 'admin-token-' + Date.now());

      ElNotification({
        title: "登录成功",
        message: '欢迎登录 管理系统',
        type: 'success',
        duration: 3000
      });

      // 跳转到用户管理页面
      router.push('/home');
    } else {
      ElNotification({
        title: "登录失败",
        message: '用户名或密码错误',
        type: 'error',
        duration: 3000
      });
    }
   

    // await initDynamicRouter();

    // tabsStore.closeMultipleTab();
    // keepAliveStore.setKeepAliveName([]);

    
    
  } finally {
    loading.value = false;
  }
};
// const captchaRef = ref<InstanceType<typeof SliderCaptcha>>();
const login = () => {
  if (!loginFormRef.value) {
    return;
  }
  loginFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return;
    }
    try {
      loading.value = true;
      // const { data } = await getCaptchaStatus(); // 获取验证码状态
      // if (data) {
      //   captchaRef.value?.acceptParams(); // 打开验证码弹窗
      // } else {
      //   await performLogin(); // 执行登录
      // }
      await performLogin(); // 执行登录
    } finally {
      loading.value = false;
    }
  });
};

const resetForm = () => {
  if (!loginFormRef.value) return;
  loginFormRef.value.resetFields();
};


onMounted(() => {
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === 'Enter' || e.code === 'enter' || e.code === 'NumpadEnter') {
      if (loading.value) return;
      login();
    }
  };
});
</script>

<style scoped lang="scss">
.login-container {
  height: 100%;
  min-height: 550px;
  background-color: #eeeeee;
//   background-image: url('@/assets/images/login_bg.svg');
  background-size: cover;

  .login-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 100%;
    padding: 0 50px;
    background-color: rgb(255 255 255 / 80%);
    border-radius: 10px;

    .dark {
      position: absolute;
      top: 13px;
      right: 18px;
    }

    .login-left {
      width: 800px;
      margin-right: 10px;

      .login-left-img {
        width: 100%;
        height: 100%;
      }
    }

    .login-form {
      width: 420px;
      padding: 50px 40px 45px;
      background-color: var(--el-bg-color);
      border-radius: 10px;
      box-shadow: rgb(0 0 0 / 10%) 0 2px 10px 2px;

      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;

        .login-icon {
          width: 60px;
          height: 52px;
        }

        .logo-text {
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 42px;
          font-weight: bold;
          color: #34495e;
          white-space: nowrap;
        }
      }

      .el-form-item {
        margin-bottom: 40px;
      }

      .login-btn {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 40px;
        white-space: nowrap;

        .el-button {
          width: 185px;
        }
      }
    }
  }
}

@media screen and (width <= 1250px) {
  .login-left {
    display: none;
  }
}

@media screen and (width <= 600px) {
  .login-form {
    width: 97% !important;
  }
}

</style>
