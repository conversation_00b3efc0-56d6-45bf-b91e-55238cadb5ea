<template>
  <div class="dashboard-container">
    <!-- 第一部分：指标卡片 -->
    <el-card class="section-header-card">
      <div class="section-title">关键指标概览</div>
      <div class="date-picker-container">
        <span style="margin-right: 10px;">开始日期</span>
        <el-date-picker
          v-model="startDate"
          type="month"
          placeholder="开始时间"
          format="YYYY-MM"
          value-format="YYYY-MM"
          style="width: 120px; margin-right: 10px;"
        />
        <span style="margin-right: 10px;">结束日期</span>
        <el-date-picker
          v-model="endDate"
          type="month"
          placeholder="结束时间"
          format="YYYY-MM"
          value-format="YYYY-MM"
          style="width: 120px;"
        />
      </div>
    </el-card>
    <el-row :gutter="20" class="section">
      <el-col :span="24" :md="6" v-for="(item, index) in kpiCards" :key="index">
        <el-card class="profit-card">
          <div class="card-content">
            <div class="card-left">
              <div class="first-line">
                <span class="title">{{ item.title }}</span>
                 <el-tooltip 
                  v-if="item.desc" 
                  :content="item.desc" 
                  placement="top"
                >
                  <el-icon class="info-icon"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="second-line">
                <span class="amount">
                  <template v-if="index !== 1">
                    {{ item.value }}
                    <span
                      v-if="item.ratio !== undefined"
                      class="ratio"
                      :class="{
                        'ratio-positive': item.ratio > 0,
                        'ratio-negative': item.ratio < 0,
                        'ratio-zero': item.ratio === 0
                      }"
                    >
                      {{ item.ratio > 0 ? '较上月+' : '较上月' }}{{ formatUnitFriendly(item.ratio) }}
                    </span>
                    <div class="additional-info">
                    </div>
                  </template>
                </span>
                <div v-if="index === 1" class="staff-counts-container">
                  <div v-for="(staff, position) in item.staffCounts" :key="position" class="staff-count-item">
                    {{ position }}: {{ staff.count }}
                    <span
                      class="ratio"
                      :class="{
                        'ratio-positive': staff.ratio > 0,
                        'ratio-negative': staff.ratio < 0,
                        'ratio-zero': staff.ratio === 0
                      }"
                    >
                      {{ staff.ratio > 0 ? '较上月+' : '较上月' }}{{ staff.ratio }}
                    </span>
                  </div>
                </div>
              </div>
              <!-- 人均数指标显示 -->
              <div v-if="srcsys == 2 && (index === 4 || index === 5 || index === 7) && item.perCapitaValue" class="per-capita-line">
                <span class="per-capita-label">人均:</span>
                <span class="per-capita-value">{{ item.perCapitaValue }}</span>
              </div>
              <div v-if="index > 3 && index != 6" class="third-line">
                <span class="score">考核指标得分: {{ item.score }}</span>
              </div>
            </div>
            <div class="card-right">
              <el-icon :size="60" color="#409EFF">
                <component :is="item.icon" />
              </el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 关键指标分布情况 -->
    <el-card class="section-header-card" v-if="srcsys === '1'">
      <div class="section-title">关键指标分布情况</div>
    </el-card>
    <div class="section distribution-container" v-if="srcsys === '1'">
      <div class="distribution-wrapper" v-for="(distribution, index) in scoreDistribution" :key="index">
        <el-card class="distribution-table-card">
          <div class="distribution-table-title">{{ distribution.title }}</div>
          <el-table
            :data="distribution.data"
            style="width: 100%"
            class="distribution-table"
            :fit="true">
            <el-table-column prop="range" label="区间" align="center" />
            <el-table-column prop="count" label="人数" align="center" />
          </el-table>
        </el-card>
      </div>
    </div>

    <!-- 第二部分：营销人员排名 -->
    <el-card class="section-header-card">
      <div class="section-title">
        {{ srcsys == '1' ? '营销人员考核指标排名' : '营销人员排名' }}
        <el-tooltip 
          v-if="srcsys == '1'" 
          content="统计数据为各分支机构营销人员业绩数据" 
          placement="top"
        >
          <el-icon class="info-icon"><InfoFilled /></el-icon>
        </el-tooltip>
      </div>
    </el-card>
    <el-row v-for="(group, groupIndex) in rankingGroups" :key="groupIndex" :gutter="20" class="section">
      <el-col v-for="ranking in group" :key="ranking.title" :span="8">
        <el-card class="ranking-card">
          <div class="ranking-header">
            <div class="ranking-title">{{ ranking.title }}</div>
              <div v-if="myRankings[ranking.title]" class="my-ranking-info">
              <span>我的排名: {{ myRankings[ranking.title].rank }}</span>
              <span v-if=" ranking.title !== '投产比排名'">我的得分: {{ myRankings[ranking.title].score }}</span>
            </div>
          </div>
          <el-table :data="ranking.data" style="width: 100%" height="250" :row-class-name="rowClassName">
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="name" :label="srcsys === '1' ? '分支机构名称' : '营销人员姓名'" align="center">
              <template #default="scope">
                <span
                  :style="srcsys === '1' ? { cursor: 'pointer', color: '#409EFF' } : {}"
                  @click="srcsys === '1' && handleNameClick(scope.row)"
                >
                  {{ scope.row.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column :label="ranking.title.replace('排名', '')" width="120" align="center">
              <template #default="scope">
                {{ ranking.valueFormatter ? ranking.valueFormatter(scope.row.value) : scope.row.score }}
              </template>
            </el-table-column>
            <el-table-column v-if="ranking.title !== '考核得分排名' && ranking.title !== '投产比排名'" prop="score" label="得分" width="120" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第三部分：趋势图 -->
    <el-card class="section-header-card">
      <div class="section-title">趋势分析</div>
    </el-card>
    <el-row class="section">
      <el-card class="trend-card">
        <el-row :gutter="20">
          <el-col :span="12" v-for="(item, index) in trendCharts" :key="index" style="margin-bottom: 20px;">
            <el-card class="trend-item">
              <div class="trend-title">{{ item.title }}</div>
              <div :ref="el => setTrendChartRef(el, index)" class="trend-chart"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue';
import * as echarts from 'echarts';
import { formatUnitFriendly } from '~/utils/format';
import { useRoute, useRouter } from 'vue-router'
import { InfoFilled, User, Briefcase, Trophy, DataAnalysis, Money, Coin, Box, Document, Suitcase } from '@element-plus/icons-vue';

const router = useRouter()
const route = useRoute()

// 获取URL参数
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')
const branchCode = ref(route.query.branchCode || '21')
// 计算默认日期（上一个月），并格式化为 YYYY-MM 字符串
const getDefaultDate = () => {
  const date = new Date();
  date.setMonth(date.getMonth() - 1);
  return date.toISOString().slice(0, 7); // 返回 YYYY-MM 格式的字符串
};

const startDate = ref(getDefaultDate());
const endDate = ref(getDefaultDate());

// 第一部分：指标卡片数据
const kpiCards = ref([
  { 
    title: '在职员工数量', 
    value: formatUnitFriendly(Math.floor(Math.random() * 200) + 50, 0), 
    icon: User,
    score: Math.floor(Math.random() * 50) + 50,
    ratio: Math.round(parseFloat(Math.random() * 100))
  },
  { 
    title: '各岗位人数', 
    value: '', 
    icon: Briefcase,
    staffCounts: {
      '分支机构负责人': { count: Math.floor(Math.random() * 20) + 5, ratio: Math.round(Math.random() * 10) - 5 },
      '综合营运岗': { count: Math.floor(Math.random() * 15) + 3, ratio: Math.round(Math.random() * 10) - 5 },
      '市场部经理': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '理财经理': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '营销管培生': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '机构业务岗': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '副总经理': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '业务运营岗': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 },
      // '业务拓展岗': { count: Math.floor(Math.random() * 10) + 2, ratio: Math.round(Math.random() * 10) - 5 }
    },
    score: Math.floor(Math.random() * 50) + 50,
    // ratio: parseFloat((Math.random() * 2 - 1).toFixed(2))
  },
  { 
    title: '平均考核得分', 
    value: formatUnitFriendly((Math.random() * 20) + 80, 1), 
    icon: Trophy,
    score: Math.floor(Math.random() * 20) + 80,
    ratio: 0
  },
  { 
    title: '人均投产比', 
    value: formatUnitFriendly((Math.random() * 0.5) + 0.5, 2), 
    icon: DataAnalysis,
    score: Math.floor(Math.random() * 20) + 80,
    ratio: parseFloat((Math.random() * 10).toFixed(2))
  },
  { 
    title: '日均权益', 
    value: formatUnitFriendly(Math.floor(Math.random() * 50000) + 10000000, 2), 
    perCapitaValue: formatUnitFriendly((Math.floor(Math.random() * 50000) + 10000000) / (Math.floor(Math.random() * 200) + 50), 2),
    icon: Money,
    score: Math.floor(Math.random() * 50) + 50,
    ratio: parseFloat(Math.random() * 1000000),
    desc: '所有营销人员的考核日均权益汇总，包括创新业务折算权益'
  },
  { 
    title: '净收入', 
    value: formatUnitFriendly(Math.floor(Math.random() * 10000) + 150000, 2), 
    perCapitaValue: formatUnitFriendly((Math.floor(Math.random() * 10000) + 150000) / (Math.floor(Math.random() * 200) + 50), 2),
    icon: Coin,
    score: Math.floor(Math.random() * 50) + 50,
    ratio: parseFloat(Math.random() * 100000),
    desc: '所有营销人员的净收入汇总'
  },
  { 
    title: '净入金', 
    value: formatUnitFriendly(Math.floor(Math.random() * 10000) + 100000, 2), 
    icon: Suitcase,
   // score: Math.floor(Math.random() * 50) + 50,
    ratio: parseFloat(Math.random() * 100000 + 5000),
    desc: '所有营销人员的新增净入金汇总'
  },
  { 
    title: '新增有效户', 
    value: formatUnitFriendly(Math.floor(Math.random() * 300) + 100, 0), 
    perCapitaValue: formatUnitFriendly((Math.floor(Math.random() * 300) + 100) / (Math.floor(Math.random() * 200) + 50), 0),
    icon: Box,
    score: Math.floor(Math.random() * 20) + 80,
    ratio: Math.round(parseFloat(Math.random() * 100) - 200),
    desc: '所有营销人员的考核有效户汇总，包括创新业务折算有效户'
  }
]);

// 第二部分：营销人员排名数据
const myRankings = ref({});

const generateRankingData = (title, scoreKey, valueFormatter) => {
  const generateValue = () => {
    if (title === '新增有效户排名') {
      return Math.floor(Math.random() * 101); // 0-100的整数
    }
    if (title === '投产比排名') {
      return parseFloat((Math.random() * 2).toFixed(2)); // 0-2的两位小数
    }
    return Math.random() * 100000;
  };

  // 生成20条示例数据而不是随机数据
  const data = Array.from({ length: 19 }, (_, i) => ({
    rank: i + 1,
    name: srcsys.value === '1' ? `分支机构${i + 1}` : `营销人员${i + 1}`,
    score: Math.floor(Math.random() * 50) + 50,
    value: generateValue(),
  }));

  // 插入"我"的数据
  const myData = {
    name: srcsys.value === '2' ? '营销人员21' :  '分支机构21',
    score: Math.floor(Math.random() * 50) + 50,
    value: generateValue(),
  };
  data.push(myData);

  // 排序并分配排名
  const sortedData = data
    .sort((a, b) => b.score - a.score)
    .map((item, index) => ({ ...item, rank: index + 1 }));

  const myRankInfo = sortedData.find(item => item.name === '营销人员21');

  if(srcsys.value === '2') {
      myRankings.value[title] = myRankInfo;
  }

  return {
    title,
    data: sortedData, // 直接返回数据而不是包装在ref中
    scoreKey,
    valueFormatter,
    myRank: myRankInfo != undefined ? myRankInfo.rank : -1,
    myScore:  myRankInfo != undefined ? myRankInfo.score : 0,
  };
};

const rankingGroups = ref([
  [
    generateRankingData('考核得分排名', 'score'),
    generateRankingData('日均权益排名', 'equity', value => formatUnitFriendly(value, 2)),
    generateRankingData('净收入排名', 'income', value => formatUnitFriendly(value, 1)),
    
   
  ],
  [
    generateRankingData('投产比排名', 'investmentRatio', value => formatUnitFriendly(value, 2)),
    generateRankingData('新增有效户排名', 'newUsers', value => formatUnitFriendly(value, 0)),
    generateRankingData('净入金排名', 'inamt', value => formatUnitFriendly(value, 1)),
  ],
]);

const scoreDistribution = computed(() => {
  const distribution = [
    {
      scoreRange: '[80, 100]',
      scoreCount: 6,
      equityRange: '5000以上',
      equityCount: 6,
      incomeRange: '5000以上',
      incomeCount: 6,
      newUserRange: '4户以上',
      newUserCount: 6,
      investorRatioRange: '3以上',
      investorRatioCount: 6
    },
    {
      scoreRange: '[60, 80)',
      scoreCount: 16,
      equityRange: '[3000, 5000)',
      equityCount: 16,
      incomeRange: '[3000, 5000)',
      incomeCount: 16,
      newUserRange: '[3, 4)',
      newUserCount: 16,
      investorRatioRange: '[2, 3)',
      investorRatioCount: 16
    },
    {
      scoreRange: '[40, 60)',
      scoreCount: 37,
      equityRange: '[1000, 3000)',
      equityCount: 37,
      incomeRange: '[1000, 3000)',
      incomeCount: 37,
      newUserRange: '[2, 3)',
      newUserCount: 37,
      investorRatioRange: '[1, 2)',
      investorRatioCount: 37
    },
    {
      scoreRange: '[20, 40)',
      scoreCount: 13,
      equityRange: '[500, 1000)',
      equityCount: 13,
      incomeRange: '[500, 1000)',
      incomeCount: 13,
      newUserRange: '[1, 2)',
      newUserCount: 13,
      investorRatioRange: '[0.5, 1)',
      investorRatioCount: 13
    },
    {
      scoreRange: '[0, 20)',
      scoreCount: 3,
      equityRange: '[0, 500)',
      equityCount: 3,
      incomeRange: '[0, 500)',
      incomeCount: 3,
      newUserRange: '[0, 1)',
      newUserCount: 3,
      investorRatioRange: '[0, 0.5)',
      investorRatioCount: 3
    }
  ];

  // 如果有实际的排名数据，则更新得分分布的人数
  if (rankingGroups.value.length > 0 && rankingGroups.value[0].length > 0) {
    const scoreRankingData = rankingGroups.value[0][0].data;

    // 重置人数统计
    distribution.forEach(item => {
      item.scoreCount = 0;
    });

    scoreRankingData.forEach(person => {
      const score = person.score;
      if (score >= 80 && score <= 100) {
        distribution[0].scoreCount++;
      } else if (score >= 60 && score < 80) {
        distribution[1].scoreCount++;
      } else if (score >= 40 && score < 60) {
        distribution[2].scoreCount++;
      } else if (score >= 20 && score < 40) {
        distribution[3].scoreCount++;
      } else if (score >= 0 && score < 20) {
        distribution[4].scoreCount++;
      }
    });
  }

  // 拆分为5个独立的数据结构，每个对应一个表格
  return [
    {
      title: '得分分布',
      data: distribution.map(item => ({
        range: item.scoreRange,
        count: item.scoreCount
      }))
    },
    {
      title: '日均权益分布(万元)',
      data: distribution.map(item => ({
        range: item.equityRange,
        count: item.equityCount
      }))
    },
    {
      title: '净收入分布(万元)',
      data: distribution.map(item => ({
        range: item.incomeRange,
        count: item.incomeCount
      }))
    },
    {
      title: '新增有效户分布',
      data: distribution.map(item => ({
        range: item.newUserRange,
        count: item.newUserCount
      }))
    },
    {
      title: '投产比分布',
      data: distribution.map(item => ({
        range: item.investorRatioRange,
        count: item.investorRatioCount
      }))
    }
  ];
});

const rowClassName = ({ row }) => {
  if (row.name === '营销人员21') {
    return 'self-row';
  }
  return '';
};

// 第三部分：趋势图数据
const trendCharts = ref([
  { title: '考核得分趋势及同比' },
  { title: '日均权益趋势及同比' },
  { title: '净收入趋势及同比' },
  { title: '新增有效户趋势及同比' },
]);

const trendChartRefs = ref([]);

const generateTrendData = () => {
  const categories = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const values = categories.map(() => Math.floor(Math.random() * 50) + 20);
  const lastYearRatio = categories.map(() => Math.random());
  return { categories, values, lastYearRatio };
};

const setTrendChartRef = (el, index) => {
  if (el) {
    trendChartRefs.value[index] = el;
  }
};

// 定义点击事件处理方法
// 定义点击事件处理方法
const handleNameClick = (row) => {
  if (srcsys.value === '1') {
    const newQuery = {
      ...route.query,
      srcsys: '2',
      branchCode: row.name.includes('分支机构') ? row.name.replace('分支机构', '') : null,
    };
    const queryString = new URLSearchParams(newQuery).toString();
    window.location.search = queryString;
  }
};

onMounted(() => {
  nextTick(() => {
    trendCharts.value.forEach((chart, index) => {
      const chartDom = trendChartRefs.value[index];
      if (chartDom) {
        const myChart = echarts.init(chartDom);
        const trendData = generateTrendData();
        const option = {
          xAxis: {
            type: 'category',
            data: trendData.categories,
          },
          yAxis: [
            {
              type: 'value',
              name: '本期',
              axisLabel: {
                formatter: function(value) {
                  return formatUnitFriendly(value, 1);
                }
              }
            },
            {
              type: 'value',
              name: '同比',
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              let tooltipContent = `${params[0].axisValue}<br/>`;
              params.forEach(param => {
                if (param.seriesName === '同期增减') {
                  tooltipContent += `${param.marker}${param.seriesName}: ${(param.value * 100).toFixed(2)}%<br/>`;
                } else {
                  tooltipContent += `${param.marker}${param.seriesName}: ${formatUnitFriendly(param.value, 1)}<br/>`;
                }
              });
              return tooltipContent;
            }
          },
          legend: {
            data: ['本期', '同比'],
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemStyle: {
              borderWidth: 0
            }
          },
          grid: {
            left: '10%',
            right: '10%',
            top: '15%',
            bottom: '15%',
            containLabel: true
          },
          series: [
            {
              data: trendData.values,
              type: 'bar',
              name: '本期',
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#0039FF' },
                    { offset: 1, color: '#87CEFA' }
                  ]
                }
              }
            },
            {
              data: trendData.lastYearRatio,
              type: 'line',
              name: '同比',
              yAxisIndex: 1,
              itemStyle: {
                color: '#FF5733'
              }
            }
          ],
        };
        myChart.setOption(option);
      }
    });
  });
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.section-header-card {
  margin-bottom: 15px;
  border-left: 4px solid #409eff;
  position: relative;

  .section-title {
    float: left;
    font-size: 20px;
    font-weight: bold;
    color: #303133;
    padding: 5px 0 25px 0;
    text-align: left;
    display: flex;
    align-items: center;
    
    .info-icon {
      margin-left: 8px;
      color: #909399;
      cursor: pointer;
      font-size: 16px;
    }
  }
}

.date-picker-container {
  float: left;
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.date-picker-container :deep(.el-input__wrapper) {
  height: 30px;
}

.date-picker-container :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
}

.section {
  margin-bottom: 30px; /* 增加卡片行间距 */
}

.distribution-container {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;

  .distribution-wrapper {
    flex: 1;
    display: flex;
    align-items: stretch;
  }
}

.profit-card {
  min-height: 180px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 10px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .card-content {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }

  .card-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .first-line {
    text-align: left;
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-right: 5px;
    }

    .info-icon {
      color: #909399;
      cursor: pointer;
      font-size: 16px;
    }
  }

  .second-line {
    text-align: left;

    .amount {
      font-size: 26px;
      font-weight: 700;
      color: #6e6e6e;
      line-height: 1.2;
      display: flex;
      align-items: baseline;
    }

    .ratio {
      font-size: 16px;
      margin-left: 10px;
      font-weight: 500;
    }

    .ratio-positive {
      color: red;
    }

    .ratio-negative {
      color: green;
    }

    .ratio-zero {
      color: #ccc;
    }

    .additional-info {
      font-size: 14px;
      color: #999;
      margin-top: 5px;
    }

    .staff-counts-container {
      margin-top: 8px;
    }
    .staff-count-item {
      font-size: 14px;
      color: #666;
      margin-bottom: 5px;
      display: block;
    }
  }
  .per-capita-line {
    text-align: left;
    margin: 5px 0;

    .per-capita-label {
      font-size: 14px;
      color: #999;
      margin-right: 5px;
    }

    .per-capita-value {
      font-size: 16px;
      font-weight: 600;
      color: #409EFF;
    }
  }

  .third-line {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
    font-weight: 400;
    text-align: left;
  }

  .fourth-line {
    margin-top: 10px;
    flex-grow: 1;
    display: flex;
    align-items: flex-end;
  }
}

.ranking-card {
  width: 100%;
  margin: 10px 10px;

  .ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .ranking-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    text-align: left;
  }

  .my-ranking-info {
    font-size: 14px;
    color: #606266;
    & > span {
      margin-left: 10px;
    }
  }
}

.trend-card {
  width: 100%;

  .trend-item {
    height: 400px;

    .trend-title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .trend-chart {
      width: 100%;
      height: 350px;
    }
  }
}

// 添加自定义行高亮样式，提高优先级
:deep(.self-row) {
  background-color: #409eff !important;
  color: white !important;
}

:deep(.self-row td) {
  background-color: #409eff !important;
  color: white !important;
  font-weight: bold;
}

// 添加悬停效果增强对比度
:deep(.self-row:hover) {
  background-color: #337ecc !important;
}

:deep(.self-row td:hover) {
  background-color: #337ecc !important;
}

// 响应式调整
@media (max-width: 768px) {
  .profit-card {
    min-height: 200px;
    margin-bottom: 15px;
  }

  .profit-card .amount {
    font-size: 14px !important;
  }

  .profit-card .title {
    font-size: 14px !important;
  }

  .profit-card .score {
    font-size: 12px !important;
  }
}

.distribution-table-card {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .distribution-table-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    text-align: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
    flex-shrink: 0;
  }

  .distribution-table {
    width: 100%;
    margin-bottom: 0;
    flex-grow: 1;

    :deep(.el-table) {
      table-layout: auto;
      height: 100%;
    }

    :deep(.el-table__header-wrapper) {
      width: 100% !important;
    }

    :deep(.el-table__body-wrapper) {
      width: 100% !important;
    }

    :deep(.el-table__header th.el-table__cell) {
      text-align: center !important;
      font-weight: 600;
      padding: 8px 0;
      background: #f5f7fa;
    }

    :deep(.el-table__body td.el-table__cell) {
      text-align: center !important;
      padding: 6px 0;
    }

    :deep(.el-table__body) {
      tr:hover > td.el-table__cell {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>