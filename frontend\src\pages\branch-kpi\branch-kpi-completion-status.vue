<template>
  <div class="report-title">
    <h2>重点考核目标完成情况</h2>

    <!-- 利润完成情况 -->
    <el-card class="kpi-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">利润完成情况</span>
          <el-button type="primary" size="small" @click="handleExport('001', '利润完成情况')" :loading="exportLoading">
            导出
          </el-button>
        </div>
      </template>
      <el-table
        :data="profitData"
        border
        stripe
        v-loading="profitLoading"
        table-layout="auto"
        class="kpi-table">
        <el-table-column prop="year_no" label="年份" width="80" fixed />
        <!-- <el-table-column prop="brh_cd" label="分支机构代码" width="120"  /> -->
        <el-table-column prop="brh_nam" label="分支机构名称" width="150" fixed />
        <el-table-column prop="sum_val" label="累计利润" width="140" align="right" fixed>
          <template #default="scope">{{ formatWanYuan(scope.row.sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="year_tgt" label="年度利润目标" width="160" align="right" fixed>
          <template #default="scope">{{ formatWanYuan(scope.row.year_tgt) }}</template>
        </el-table-column>
        <el-table-column prop="sum_val_rto" label="累计利润完成进度" width="160" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.sum_val_rto) }}</template>
        </el-table-column>
        <el-table-column prop="pre_year_sum_val" label="去年同期累计利润" width="170" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.pre_year_sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_change_amt" label="同比变动额" width="140" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.yoy_change_amt) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_chang_rto" label="同比增长幅度" width="120" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.yoy_chang_rto) }}</template>
        </el-table-column>
        <el-table-column prop="m1" label="1月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m1) }}</template>
        </el-table-column>
        <el-table-column prop="m2" label="2月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m2) }}</template>
        </el-table-column>
        <el-table-column prop="m3" label="3月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m3) }}</template>
        </el-table-column>
        <el-table-column prop="m4" label="4月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m4) }}</template>
        </el-table-column>
        <el-table-column prop="m5" label="5月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m5) }}</template>
        </el-table-column>
        <el-table-column prop="m6" label="6月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m6) }}</template>
        </el-table-column>
        <el-table-column prop="m7" label="7月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m7) }}</template>
        </el-table-column>
        <el-table-column prop="m8" label="8月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m8) }}</template>
        </el-table-column>
        <el-table-column prop="m9" label="9月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m9) }}</template>
        </el-table-column>
        <el-table-column prop="m10" label="10月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m10) }}</template>
        </el-table-column>
        <el-table-column prop="m11" label="11月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m11) }}</template>
        </el-table-column>
        <el-table-column prop="m12" label="12月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m12) }}</template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 收入完成情况 -->
    <el-card class="kpi-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">收入完成情况</span>
          <el-button type="primary" size="small" @click="handleExport('002', '收入完成情况')" :loading="exportLoading">
            导出
          </el-button>
        </div>
      </template>
      <el-table
        :data="incomeData"
        border
        stripe
        v-loading="incomeLoading"
        table-layout="auto"
        class="kpi-table">
        <el-table-column prop="year_no" label="年份" width="80"  fixed/>
        <!-- <el-table-column prop="brh_cd" label="分支机构代码" width="120"  /> -->
        <el-table-column prop="brh_nam" label="分支机构名称" width="150" fixed />
        <el-table-column prop="sum_val" label="累计收入" width="140" align="right" fixed>
          <template #default="scope">{{ formatWanYuan(scope.row.sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="year_tgt" label="年度收入目标" width="160" align="right" fixed>
          <template #default="scope">{{ formatWanYuan(scope.row.year_tgt) }}</template>
        </el-table-column>
        <el-table-column prop="sum_val_rto" label="累计收入完成进度" width="160" align="right" fixed>
          <template #default="scope">{{ formatPercentage(scope.row.sum_val_rto) }}</template>
        </el-table-column>
        <el-table-column prop="pre_year_sum_val" label="去年同期累计收入" width="170" align="right" >
          <template #default="scope">{{ formatWanYuan(scope.row.pre_year_sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_change_amt" label="同比变动额" width="140" align="right" >
          <template #default="scope">{{ formatWanYuan(scope.row.yoy_change_amt) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_chang_rto" label="同比增长幅度" width="120" align="right" >
          <template #default="scope">{{ formatPercentage(scope.row.yoy_chang_rto) }}</template>
        </el-table-column>
        <el-table-column prop="m1" label="1月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m1) }}</template>
        </el-table-column>
        <el-table-column prop="m2" label="2月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m2) }}</template>
        </el-table-column>
        <el-table-column prop="m3" label="3月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m3) }}</template>
        </el-table-column>
        <el-table-column prop="m4" label="4月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m4) }}</template>
        </el-table-column>
        <el-table-column prop="m5" label="5月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m5) }}</template>
        </el-table-column>
        <el-table-column prop="m6" label="6月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m6) }}</template>
        </el-table-column>
        <el-table-column prop="m7" label="7月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m7) }}</template>
        </el-table-column>
        <el-table-column prop="m8" label="8月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m8) }}</template>
        </el-table-column>
        <el-table-column prop="m9" label="9月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m9) }}</template>
        </el-table-column>
        <el-table-column prop="m10" label="10月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m10) }}</template>
        </el-table-column>
        <el-table-column prop="m11" label="11月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m11) }}</template>
        </el-table-column>
        <el-table-column prop="m12" label="12月" width="120" align="right">
          <template #default="scope">{{ formatWanYuan(scope.row.m12) }}</template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 日均权益完成情况 -->
    <el-card class="kpi-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">日均权益完成情况</span>
          <el-button type="primary" size="small" @click="handleExport('003', '日均权益完成情况')" :loading="exportLoading">
            导出
          </el-button>
        </div>
      </template>
      <el-table
        :data="equityData"
        border
        stripe
        v-loading="equityLoading"
        table-layout="auto"
        class="kpi-table">
        <el-table-column prop="year_no" label="年份" width="80" fixed />
        <!-- <el-table-column prop="brh_cd" label="分支机构代码" width="120"  /> -->
        <el-table-column prop="brh_nam" label="分支机构名称" width="150"  fixed/>
        <el-table-column prop="sum_val" label="累计日均权益" width="140" align="right" fixed>
          <template #default="scope">{{ formatYiYuan(scope.row.sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="year_tgt" label="年度日均权益目标" width="160" align="right" fixed>
          <template #default="scope">{{ formatYiYuan(scope.row.year_tgt) }}</template>
        </el-table-column>
        <el-table-column prop="sum_val_rto" label="累计日均权益完成进度" width="160" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.sum_val_rto) }}</template>
        </el-table-column>
        <el-table-column prop="pre_year_sum_val" label="去年同期累计日均权益" width="170" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.pre_year_sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_change_amt" label="同比变动额" width="140" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.yoy_change_amt) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_chang_rto" label="同比增长幅度" width="120" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.yoy_chang_rto) }}</template>
        </el-table-column>
        <el-table-column prop="m1" label="1月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m1) }}</template>
        </el-table-column>
        <el-table-column prop="m2" label="2月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m2) }}</template>
        </el-table-column>
        <el-table-column prop="m3" label="3月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m3) }}</template>
        </el-table-column>
        <el-table-column prop="m4" label="4月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m4) }}</template>
        </el-table-column>
        <el-table-column prop="m5" label="5月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m5) }}</template>
        </el-table-column>
        <el-table-column prop="m6" label="6月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m6) }}</template>
        </el-table-column>
        <el-table-column prop="m7" label="7月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m7) }}</template>
        </el-table-column>
        <el-table-column prop="m8" label="8月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m8) }}</template>
        </el-table-column>
        <el-table-column prop="m9" label="9月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m9) }}</template>
        </el-table-column>
        <el-table-column prop="m10" label="10月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m10) }}</template>
        </el-table-column>
        <el-table-column prop="m11" label="11月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m11) }}</template>
        </el-table-column>
        <el-table-column prop="m12" label="12月" width="120" align="right">
          <template #default="scope">{{ formatYiYuan(scope.row.m12) }}</template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 有效户完成情况 -->
    <el-card class="kpi-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">有效户完成情况</span>
          <el-button type="primary" size="small" @click="handleExport('008', '有效户完成情况')" :loading="exportLoading">
            导出
          </el-button>
        </div>
      </template>
      <el-table
        :data="validCustomerData"
        border
        stripe
        v-loading="validCustomerLoading"
        table-layout="auto"
        class="kpi-table">
        <el-table-column prop="year_no" label="年份" width="80"  />
        <!-- <el-table-column prop="brh_cd" label="分支机构代码" width="120"  /> -->
        <el-table-column prop="brh_nam" label="分支机构名称" width="150"  />
        <el-table-column prop="sum_val" label="累计有效户" width="120" align="right">
          <template #default="scope">{{ formatNumber(scope.row.sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="year_tgt" label="年度有效户目标" width="140" align="right">
          <template #default="scope">{{ formatNumber(scope.row.year_tgt) }}</template>
        </el-table-column>
        <el-table-column prop="sum_val_rto" label="累计有效户完成进度" width="160" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.sum_val_rto) }}</template>
        </el-table-column>
        <el-table-column prop="pre_year_sum_val" label="去年同期累计有效户" width="150" align="right">
          <template #default="scope">{{ formatNumber(scope.row.pre_year_sum_val) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_change_amt" label="同比变动额" width="120" align="right">
          <template #default="scope">{{ formatNumber(scope.row.yoy_change_amt) }}</template>
        </el-table-column>
        <el-table-column prop="yoy_chang_rto" label="同比增长幅度" width="120" align="right">
          <template #default="scope">{{ formatPercentage(scope.row.yoy_chang_rto) }}</template>
        </el-table-column>
        <el-table-column prop="m1" label="1月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m1) }}</template>
        </el-table-column>
        <el-table-column prop="m2" label="2月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m2) }}</template>
        </el-table-column>
        <el-table-column prop="m3" label="3月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m3) }}</template>
        </el-table-column>
        <el-table-column prop="m4" label="4月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m4) }}</template>
        </el-table-column>
        <el-table-column prop="m5" label="5月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m5) }}</template>
        </el-table-column>
        <el-table-column prop="m6" label="6月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m6) }}</template>
        </el-table-column>
        <el-table-column prop="m7" label="7月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m7) }}</template>
        </el-table-column>
        <el-table-column prop="m8" label="8月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m8) }}</template>
        </el-table-column>
        <el-table-column prop="m9" label="9月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m9) }}</template>
        </el-table-column>
        <el-table-column prop="m10" label="10月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m10) }}</template>
        </el-table-column>
        <el-table-column prop="m11" label="11月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m11) }}</template>
        </el-table-column>
        <el-table-column prop="m12" label="12月" width="100" align="right">
          <template #default="scope">{{ formatNumber(scope.row.m12) }}</template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

const route = useRoute()

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || 'system',
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || '1'
  }
}
const urlParams = getUrlParams()

// 数据状态
const profitData = ref([])
const incomeData = ref([])
const equityData = ref([])
const validCustomerData = ref([])

// 加载状态
const profitLoading = ref(false)
const incomeLoading = ref(false)
const equityLoading = ref(false)
const validCustomerLoading = ref(false)
const exportLoading = ref(false)

// 格式化数字（原始格式，用于有效户）
const formatNumber = (num) => {
  if (num === null || num === undefined || num === '') return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化万元（用于利润和收入）
const formatWanYuan = (num) => {
  if (num === null || num === undefined || num === '') return '0万元'
  const value = Number(num) / 10000
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }) + '万元'
}

// 格式化亿元（用于日均权益）
const formatYiYuan = (num) => {
  if (num === null || num === undefined || num === '') return '0亿元'
  const value = Number(num) / 100000000
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }) + '亿元'
}

// 格式化百分比
const formatPercentage = (num) => {
  if (num === null || num === undefined || num === '') return '0.00%'
  return (Number(num) * 100).toFixed(2) + '%'
}

// 获取指标数据
const fetchKpiData = async (indxCd, dataRef, loadingRef) => {
  loadingRef.value = true

  try {
    const functionParams = {
      oacode: urlParams.oacode,
      roleid: urlParams.roleid,
      srcsys: urlParams.srcsys,
      goBack: urlParams.goBack
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const response = await http.callFunction('p_kpi_brh_completion_s', functionParams, config)
    const rawData = response.data || []

    // 筛选指定指标的数据
    dataRef.value = rawData.filter(item => item.indx_cd === indxCd)

  } catch (error) {
    console.error(`获取指标${indxCd}数据失败:`, error)
    ElMessage.error(`获取指标${indxCd}数据失败`)
  } finally {
    loadingRef.value = false
  }
}

// 加载所有数据
const loadAllData = async () => {
  try {
    // 并发加载所有指标数据
    await Promise.all([
      fetchKpiData('001', profitData, profitLoading),
      fetchKpiData('002', incomeData, incomeLoading),
      fetchKpiData('003', equityData, equityLoading),
      fetchKpiData('008', validCustomerData, validCustomerLoading)
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 导出功能
const handleExport = async (indxCd, sheetName) => {
  if (exportLoading.value) return

  exportLoading.value = true

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 获取对应指标的数据
    let exportData = []
    switch (indxCd) {
      case '001':
        exportData = profitData.value
        break
      case '002':
        exportData = incomeData.value
        break
      case '003':
        exportData = equityData.value
        break
      case '008':
        exportData = validCustomerData.value
        break
    }

    if (exportData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 根据指标类型选择格式化函数和表头
    let formatFunc, headerSuffix
    switch (indxCd) {
      case '001': // 利润
        formatFunc = formatWanYuan
        headerSuffix = '利润'
        break
      case '002': // 收入
        formatFunc = formatWanYuan
        headerSuffix = '收入'
        break
      case '003': // 日均权益
        formatFunc = formatYiYuan
        headerSuffix = '日均权益'
        break
      case '008': // 有效户
        formatFunc = formatNumber
        headerSuffix = '有效户'
        break
      default:
        formatFunc = formatNumber
        headerSuffix = ''
    }

    // 准备导出数据 - 按照页面展示顺序排列
    const excelData = [
      // 表头按照页面展示顺序
      [
        '年份', '分支机构名称', `累计${headerSuffix}`, `年度${headerSuffix}目标`, `累计${headerSuffix}完成进度`,
        `去年同期累计${headerSuffix}`, '同比变动额', '同比增长幅度',
        '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'
      ],
      // 数据行
      ...exportData.map(item => [
        item.year_no || '',
        item.brh_nam || '',
        formatFunc(item.sum_val),
        formatFunc(item.year_tgt),
        formatPercentage(item.sum_val_rto),
        formatFunc(item.pre_year_sum_val),
        formatFunc(item.yoy_change_amt),
        formatPercentage(item.yoy_chang_rto),
        formatFunc(item.m1),
        formatFunc(item.m2),
        formatFunc(item.m3),
        formatFunc(item.m4),
        formatFunc(item.m5),
        formatFunc(item.m6),
        formatFunc(item.m7),
        formatFunc(item.m8),
        formatFunc(item.m9),
        formatFunc(item.m10),
        formatFunc(item.m11),
        formatFunc(item.m12)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 8 },   // 年份
      { wch: 20 },  // 分支机构名称
      { wch: 15 },  // 累计值
      { wch: 18 },  // 年度目标
      { wch: 20 },  // 完成进度
      { wch: 18 },  // 去年同期累计
      { wch: 15 },  // 同比变动额
      { wch: 15 },  // 同比增长幅度
      { wch: 12 },  // 1月
      { wch: 12 },  // 2月
      { wch: 12 },  // 3月
      { wch: 12 },  // 4月
      { wch: 12 },  // 5月
      { wch: 12 },  // 6月
      { wch: 12 },  // 7月
      { wch: 12 },  // 8月
      { wch: 12 },  // 9月
      { wch: 12 },  // 10月
      { wch: 12 },  // 11月
      { wch: 12 }   // 12月
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    // 生成文件名
    const now = new Date()
    const fileName = `${sheetName}_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${exportData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  } finally {
    exportLoading.value = false
  }
}

// 初始化数据
onMounted(() => {
  loadAllData()
})
</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin: 0 0 20px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
}

.kpi-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.kpi-table {
  :deep(.el-table__header-wrapper) {
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        white-space: nowrap;

        .cell {
          padding: 8px 12px;
          text-align: center;
          vertical-align: middle;
        }
      }
    }
  }

  :deep(.el-table__body-wrapper) {
    .el-table__body {
      td {
        .cell {
          padding: 8px 12px;
          max-width: 400px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:hover {
            overflow: visible;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
    }
  }

  // 固定列样式
  :deep(.el-table__fixed-left) {
    box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  }

  // 数字列右对齐
  :deep(.el-table__body) {
    .el-table__row {
      td:nth-child(n+4) {
        text-align: right;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .kpi-table {
    font-size: 12px;

    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th .cell {
          padding: 6px 8px;
        }
      }
    }

    :deep(.el-table__body-wrapper) {
      .el-table__body {
        td .cell {
          padding: 6px 8px;
        }
      }
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

// 卡片阴影效果
.kpi-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
</style>
