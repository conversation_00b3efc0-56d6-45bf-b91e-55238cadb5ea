<template>
  <div class="dashboard-container">
    <!-- 第一部分：4个指标卡片 -->
    <el-card class="section-header-card">
      <div class="section-title">考核完成进度</div>
    </el-card>
    <el-row :gutter="20" class="section">
      <el-col :span="24" :md="6" v-for="(item, index) in profitCards" :key="item.indx_cd">
        <el-card class="profit-card">
          <div class="card-content">
            <el-row class="first-line">
              <el-col :span="9" align="left">
                <span class="title">{{ item.mon_range }}{{ item.indx_nam }}</span>
              </el-col>
              <el-col :span="15" style="text-align: right;">
                <span class="score">指标考核平均得分:{{ item.avg_exam_indx_scor || 0 }}</span>
              </el-col>
            </el-row>
            <el-row class="second-line">
              <el-col :span="12" align="left">
                <span class="amount" :style="{ color: (item.yoy_curr_tgt || 0) >= 0 ? 'red' : 'green' }">
                  {{ formatUnitFriendly(item.exam_indx_comp_val || 0, item.indx_nam && item.indx_nam.includes('日均权益') ? 2 : 1)}}{{item.indx_nam && item.indx_nam.includes('新增有效户') ? '户' : '元' }}
                </span>
                <span class="growth" :class="{ positive: (item.yoy_curr_tgt || 0) > 0 }">
                  (同比{{ (item.yoy_curr_tgt || 0) > 0 ? '+' : '' }}{{ item.yoy_curr_tgt || 0 }}%)
                </span>
              </el-col>
              <el-col :span="12" style="text-align: right;">
                <span>
                  完成进度 {{ item.completion_rate || 0 }}%
                </span>
              </el-col>
            </el-row>
            <el-row class="second-line">
              <el-col :span="16" align="left">
                <span class="title">年度目标</span>
              </el-col>
            </el-row>
            <el-row class="second-line">
              <el-col :span="12" align="left">
                <span class="amount">
                  {{ formatUnitFriendly(item.year_tgt || 0, item.indx_nam && item.indx_nam.includes('日均权益') ? 2 : 1) }}元
                </span>
              </el-col>
            </el-row>
            <el-row class="fourth-line">
              <el-col :span="24">
                <el-tooltip
                  :content="`年度目标: ${formatUnitFriendly(item.year_tgt || 0, item.indx_nam && item.indx_nam.includes('日均权益') ? 2 : 1, 2)}${item.indx_nam && item.indx_nam.includes('新增有效户') ? '户' : '元'}, 完成进度: ${formatUnitFriendly(item.exam_indx_comp_val || 0, item.indx_nam && item.indx_nam.includes('日均权益') ? 2 : 1, 2)}${item.indx_nam && item.indx_nam.includes('新增有效户') ? '户' : '元'}`"
                  placement="top"
                  :show-after="200"
                >
                  <el-progress
                    :percentage="Number(item.completion_rate || 0)"
                    :color="processColors"
                    :show-text="true"
                    :stroke-width="20"
                    style="margin-top: 10px;"
                  />
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二部分：分支机构排名 -->
    <el-card class="section-header-card">
      <div class="section-title">分支机构排名</div>
    </el-card>
    <el-row :gutter="20" class="section" v-if="branchRankings.length > 0">
      <el-col :span="8" v-for="ranking in branchRankings" :key="ranking.indx_cd">
        <el-card class="ranking-card">
          <div class="ranking-title">{{ ranking.mon_range }}月{{ ranking.indx_nam }}排名</div>
          <el-table
            :data="ranking.data"
            style="width: 100%"
            height="250"
            :row-class-name="({row}) => row.isSelf ? 'self-row' : ''">
            <el-table-column prop="rank_no" label="排名" width="80" align="center" />
            <el-table-column prop="brh_nam" label="分支机构名称" align="center" />
            <el-table-column
              v-if="(ranking.indx_nam.includes('日均权益') || ranking.indx_nam.includes('新增有效户') || ranking.indx_nam.includes('留存手续费')) || 
                    (srcsys !== '2' && (ranking.indx_nam.includes('利润') || ranking.indx_nam.includes('收入')))"
              prop="index_value"
              :label="`${ranking.mon_range}${ranking.indx_nam}`"
              width="120"
              align="right">
              <template #default="scope">
                <span>
                  {{ formatUnitFriendly(scope.row.index_value || 0, ranking.indx_nam && ranking.indx_nam.includes('日均权益') ? 2 : 1, 2) }}{{ ranking.indx_nam && ranking.indx_nam.includes('新增有效户') ? '户' : '元' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
               v-if="!ranking.indx_nam.includes('留存手续费')"
               prop="indx_scor" label="得分" width="120" align="right" >
               <template #default="scope">
                <span>{{ scope.row.indx_scor.toFixed(2) || 0.00 }}</span>
              </template>              
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else description="暂无排名数据" />

    <!-- 第三部分：趋势图 -->
    <el-card class="section-header-card">
      <div class="section-title">趋势分析</div>
    </el-card>
    <el-row class="section" v-if="trendCharts.length > 0">
      <el-card class="trend-card">
        <el-row :gutter="20">
          <el-col :span="12" v-for="(trend, index) in trendCharts" :key="trend.title" style="margin-bottom: 20px;">
            <el-card class="trend-item">
              <div class="trend-title">{{ trend.title }}</div>
              <div v-if="trend.data && trend.data.length > 0" :ref="el => setTrendChartRef(el, index)" class="trend-chart"></div>
              <div v-else class="trend-chart">
                <el-empty description="暂无趋势数据" :image-size="100" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </el-row>
    <el-empty v-else description="暂无趋势数据" />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUpdate, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { formatNumber, formatFriendly, formatUnitFriendly } from '~/utils/format'
import http from '~/http/http.js'

const router = useRouter()
const route = useRoute()

const profitCards = ref([])
const branchRankings = ref([])
const trendCharts = ref([])

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')
const roleid = ref(route.query.roleid || '0')


// 进度条自定义颜色
const processColors = [
  { color: '#F05025', percentage: 50 },
  { color: '#5B7BFB', percentage: 80 },
  { color: '#16D585', percentage: 100 }
]

// 趋势图表引用数组
const trendChartRefs = ref([])

// 确保在每次更新前清空 refs
onBeforeUpdate(() => {
  trendChartRefs.value = []
})

// 设置趋势图表引用
const setTrendChartRef = (el, index) => {
  if (el) {
    trendChartRefs.value[index] = el
  }
}

// 使用 watch 来响应 trendCharts 的变化并初始化图表
watch(trendCharts, (newTrends) => {
  nextTick(() => {
    newTrends.forEach((trend, index) => {
      const chartDom = trendChartRefs.value[index]
      if (trend.data && trend.data.length > 0 && chartDom) {
        const chart = echarts.init(chartDom)
        const dataLength = trend.data.length
        
        // 使用相对单位适配不同分辨率
        // 基于容器宽度动态计算柱子宽度，实现12等分效果
        const containerWidth = chartDom.clientWidth
        // 假设最多显示12个月数据，将容器宽度等分为12份
        const unitWidth = containerWidth / 12
        // 每组柱子占据的宽度
        const groupWidth = unitWidth * 0.8  // 80%宽度用于柱子，20%用于间距
        // 单个柱子宽度(两个柱子)
        const barWidth = groupWidth / 2 * 0.9  // 留10%间距给两个柱子之间
        
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function(params) {
              let tooltipContent = `${params[0].axisValue}<br/>`;
              params.forEach(param => {
                if (param.seriesName === '同比增速') {
                  tooltipContent += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)}%<br/>`;
                } else {
                  const unitType = (trend.indx_nam && trend.indx_nam.includes('日均权益') && srcsys.value === '1') ? 2 : 1;
                  const formattedValue = formatUnitFriendly(param.value, unitType, 2);
                  const unit = trend.indx_nam && trend.indx_nam.includes('新增有效户') ? '户' : '元';
                  tooltipContent += `${param.marker}${param.seriesName}: ${formattedValue}${unit}<br/>`;
                }
              });
              return tooltipContent;
            }
          },
          legend: {
            data: ['本期', '去年同期', '同比增速'],
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemStyle: {
              borderWidth: 0
            }
          },
          grid: {
            left: '10%',
            right: '15%',
            top: '15%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: trend.data.map(item => item.month)
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                formatter: function(value) {
                  const unitType = (trend.indx_nam && trend.indx_nam.includes('日均权益') && srcsys.value === '1') ? 2 : 1;
                  return formatUnitFriendly(value, unitType, 2) + "元";
                }
              }
            },
            {
              type: 'value',
              position: 'right',
              axisLabel: {
                formatter: function(value) {
                  return value + '%';
                }
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '本期',
              type: 'bar',
              barWidth: barWidth,
              barGap: '10%',
              data: trend.data.map(item => item.value),
              // itemStyle: {
              //   color: '#5776CB'
              // }
              itemStyle: {
                // 加强蓝色渐变效果
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#0039FF' },
                    { offset: 1, color: '#87CEFA' }
                  ]
                }
              }
            },
            {
              name: '去年同期',
              type: 'bar',
              barWidth: barWidth,
              barGap: '10%',
              data: trend.data.map(item => item.lastYear),
              // itemStyle: {
              //   color: '#E38030'
              // }
              itemStyle: {
                // 加强绿色渐变效果
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#a6a6a6' },
                    { offset: 1, color: '#d9d9d9' }
                  ]
                }
              }
            },
            {
              name: '同比增速',
              type: 'line',
              yAxisIndex: 1,
              data: trend.data.map(item => item.yoy_rto),
              itemStyle: {
                color: '#FF6B6B'
              },
              lineStyle: {
                width: 3
              },
              symbol: 'circle',
              symbolSize: 8
            }
          ]
        }
        chart.setOption(option)
      }
    })
  })
}, { deep: true })

const fetchCompletionProgress = async () => {
  try {
    const payload = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      roleid: roleid.value
    }

    const response = await http.callFunction('p_kpi_completion_progress_s', payload, {
      headers: {
        'Content-Profile': 'mkt_base'
      }
    })

    if (response.data) {
      profitCards.value = response.data
    }
  } catch (error) {
    console.error('获取考核完成进度度失败:', error)
    ElMessage.error('获取考核完成进度失败')
  }
}

const fetchBranchRankings = async () => {
  const expectedRankings = ['考核得分', '利润', '收入', '日均权益', '新增有效户', '留存手续费']
  try {
    const payload = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      roleid: roleid.value
    }
    const response = await http.callFunction('p_kpi_brh_rank_s', payload, {
      headers: {
        'Content-Profile': 'mkt_base'
      }
    })

    let rankings = []
    if (response.data && response.data.length > 0) {
      const groupedRankings = response.data.reduce((acc, item) => {
        if (!acc[item.indx_cd]) {
          acc[item.indx_cd] = {
            indx_cd: item.indx_cd,
            indx_nam: item.indx_nam,
            mon_range: item.mon_range,
            data: []
          }
        }
        const processedItem = { ...item, isSelf: item.brh_cd === item.myself_brh_cd }
        acc[item.indx_cd].data.push(processedItem)
        return acc
      }, {})
      rankings = Object.values(groupedRankings)
    }

    const mon_range = (response.data && response.data.length > 0) ? response.data[0].mon_range : ''
    rankings.sort((a, b) => {
      const indexA = expectedRankings.indexOf(a.indx_nam)
      const indexB = expectedRankings.indexOf(b.indx_nam)
      
      if (indexA === -1 && indexB === -1) return 0
      if (indexA === -1) return 1
      if (indexB === -1) return -1
      return indexA - indexB
    })

    expectedRankings.forEach((name, index) => {
      const exists = rankings.some(r => r.indx_nam === name)
      if (!exists) {
        rankings.push({
          indx_cd: `placeholder-${index}`,
          indx_nam: name,
          mon_range,
          data: []
        })
      }
    })

    branchRankings.value = rankings
  } catch (error) {
    console.error('获取分支机构排名失败:', error)
    ElMessage.error('获取分支机构排名失败')
    // 即使接口失败，也显示所有占位
    branchRankings.value = expectedRankings.map((name, index) => ({
      indx_cd: `placeholder-error-${index}`,
      indx_nam: name,
      mon_range: '',
      data: []
    }))
  }
}

const fetchTrendCharts = async () => {
  const expectedTrends = ['利润', '收入', '日均权益', '留存手续费']
  try {
    const payload = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      roleid: roleid.value
    }
    const response = await http.callFunction('p_kpi_trend_analysis_s', payload, {
      headers: {
        'Content-Profile': 'mkt_base'
      }
    })

    let trends = []
    if (response.data && response.data.length > 0) {
      const groupedTrends = response.data.reduce((acc, item) => {
        const key = item.indx_cd
        if (!acc[key]) {
          acc[key] = {
            title: `${item.mon_range}${item.indx_nam}趋势`,
            indx_nam: item.indx_nam, 
            indx_cd: item.indx_cd,
            data: []
          }
        }

        const randomFactor = 1 + (Math.random() * 0.2 - 0.1) // 0.9到1.1之间的随机数
        const adjustedValue = (item.curr_indx_value || 0) * randomFactor
        acc[key].data.push({
          month: `${item.mon_no}月`,
          value: item.curr_indx_value || 0,
          lastYear: item.last_year_indx_value || 0,
          yoy_rto: item.yoy_rto || 0
        })
        return acc
      }, {})

      for (const key in groupedTrends) {
        groupedTrends[key].data.sort((a, b) => parseInt(a.month) - parseInt(b.month))
      }
      trends = Object.values(groupedTrends)
      trends.sort((a, b) => a.indx_cd.localeCompare(b.indx_cd))
    }

    const mon_range = (response.data && response.data.length > 0) ? response.data[0].mon_range : ''
    expectedTrends.forEach(name => {
      const exists = trends.some(t => t.indx_nam === name)
      if (!exists) {
        trends.push({
          title: `${mon_range}${name}趋势`,
          indx_nam: name,
          data: []
        })
      }
    })

    trendCharts.value = trends
    
  } catch (error) {
    console.error('获取趋势分析数据失败:', error)
    ElMessage.error('获取趋势分析数据失败')
    // 即使接口失败，也显示所有占位
    trendCharts.value = expectedTrends.map(name => ({
      title: `${name}趋势`,
      data: []
    }))
    
  }
}


// 组件挂载后初始化图表
onMounted(async () => {
  await Promise.all([
    fetchCompletionProgress(),
    fetchBranchRankings(),
    fetchTrendCharts()
  ])
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.section-header-card {
  margin-bottom: 15px;
  border-left: 4px solid #409eff;

  .section-title {
    font-size: 20px;
    font-weight: bold;
    color: #303133;
    padding: 5px 0;
    text-align: left;
  }
}

.section {
  margin-bottom: 30px;
}

.profit-card {
  min-height: 220px; // 设置最小高度，确保在小屏幕上也能完整显示所有内容

  .card-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .first-line {
    display: flex;
    justify-content: space-between;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    .score {
      font-weight: bold;
      font-size: 14px; // 当减小字体，避免换行
    }
  }

  .second-line {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .amount {
      font-size: 16px; // 适当减小字体
      font-weight: bold;
    }

    .growth {
      color: #f56c6c;
      font-size: 12px; // 适当减小字体

      &.positive {
        color: #67c23a;
      }
    }
  }

  .third-line {
    color: #909399;
    font-size: 14px;
    margin-top: 5px;
  }

  .fourth-line {
    margin-top: 10px;
    flex-grow: 1;
    display: flex;
    align-items: flex-end;
  }
}

.ranking-card {
  width: 100%;
  margin: 10px 10px;

  .ranking-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
    text-align: left;
  }
}

.trend-card {
  width: 100%;

  .trend-item {
    height: 400px;

    .trend-title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .trend-chart {
      width: 100%;
      height: 350px;
    }
  }
}

// 添加自定义行高亮样式，提高优先级
:deep(.self-row) {
  background-color: #409eff !important; // 使用更鲜明的蓝色
  color: white !important;
}

:deep(.self-row td) {
  background-color: #409eff !important;
  color: white !important;
  font-weight: bold;
}

// 添加悬停效果增强对比度
:deep(.self-row:hover) {
  background-color: #337ecc !important;
}

:deep(.self-row td:hover) {
  background-color: #337ecc !important;
}

// 响应式调整
@media (max-width: 768px) {
  .profit-card {
    min-height: 200px;
    margin-bottom: 15px;
  }

  .profit-card .amount {
    font-size: 14px !important;
  }

  .profit-card .title {
    font-size: 14px !important;
  }

  .profit-card .score {
    font-size: 12px !important;
  }
}
</style>