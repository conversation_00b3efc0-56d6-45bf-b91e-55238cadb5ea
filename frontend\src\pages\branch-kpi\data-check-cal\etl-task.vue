<template>
  <div class="report-title">
    <h2>数据计算任务列表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="任务名称">
              <el-input v-model="queryForm.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="任务类型">
              <el-select v-model="queryForm.taskType" placeholder="请选择任务类型" clearable>
                <el-option label="业务跑批" value="0" />
                <el-option label="数据采集" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="queryForm.runsStats" placeholder="请选择状态" clearable>
                <el-option label="成功" value="S" />
                <el-option label="失败" value="F" />             
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24"  style="text-align: right;">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border stripe v-loading="loading" table-layout="auto" @sort-change="handleSortChange">
        <el-table-column prop="task_id" label="任务编号" />
        <el-table-column prop="task_name" label="任务名称"/>
        <el-table-column prop="task_type" label="任务类型">
          <template #default="{ row }">
            {{ taskTypeLabel(row.task_type) }}
          </template>
        </el-table-column>
         <el-table-column prop="syetem_type" label="系统类型">
          <template #default="{ row }">
            {{ systemTypeLabel(row.syetem_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="runs_stats" label="状态">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.runs_stats)">
              {{ statusLabel(row.runs_stats) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="程序"/>
        <el-table-column prop="params" label="期间"/>
        <el-table-column prop="start_time" label="开始时间" />
        <el-table-column prop="end_time" label="结束时间" />
        <el-table-column prop="operator" label="操作人"/>
        <el-table-column label="操作"  width="180">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleExecute(scope.row)">执行</el-button>
            <el-button size="small" type="success" @click="handleViewRecord(scope.row)">查看记录</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 执行确认对话框 -->
    <el-dialog v-model="executeDialogVisible" title="确认执行任务" width="30%">
      <el-form :model="executeForm" label-width="120px">
        <el-form-item label="请选择日期">
          <el-date-picker
            v-model="executeForm.dataDate"
            type="month"
            placeholder="选择日期"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="executeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExecute" :loading="isExecuting">确认执行</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '~/http/http.js'
import {useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()

// oacode
const oacode = ref(route.query.oacode || 'system')
const systemType = ref('exam')

// 查询条件
const queryForm = reactive({
  taskName: '',
  taskType: '',
  runsStats: '',
  order: ''
})

// 执行表单数据
const executeForm = reactive({
  dataDate: ''
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 当前行数据
const currentRow = ref({})

const isExecuting = ref(false)

// 对话框
const executeDialogVisible = ref(false)

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询方法
const handleQuery = () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构造 PostgREST 兼容的查询参数
  const filters = {}
  if (queryForm.taskName) filters.task_name = `like.*${queryForm.taskName}*`
  if (queryForm.taskType) filters.task_type = `eq.${queryForm.taskType}`
  if (queryForm.runsStats) filters.runs_stats = `eq.${queryForm.runsStats}`
  filters.syetem_type = `eq.${systemType.value}`

  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: queryForm.order || 'update_date.desc',
    },
    headers: {
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`, // 添加 Range 请求头
      'Accept-Profile': 'mkt_mang'
    }
  }

  http.get('/etl_task_config', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 重置查询
const resetQuery = () => {
  queryForm.taskName = ''
  queryForm.taskType = ''
  queryForm.runsStats = ''
  handleQuery()
}

// 点击执行按钮
const handleExecute = (row) => {
  currentRow.value = { ...row }
  executeDialogVisible.value = true
}

// 新增方法：修改任务状态
const modifyTaskStatus = async (task_id, dataDate, oacode) => {
  const payload = {
    p_request: {
      task_id,
      params: dataDate,
      oacode
    }
  }

  try {
    const response = await http.post('/rpc/modify_task', payload, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Profile': 'mkt_mang'
      }
    })

    const { o_status, o_msg } = response.data || {}

    if (o_status !== 0) {
      throw new Error(o_msg || '修改任务状态失败')
    }

    return true
  } catch (error) {
    console.error('修改任务状态失败:', error)
    ElMessage.error('修改任务状态失败')
    return false
  }
}

// 添加任务日志
const addTaskLog = async (task_id, task_name, dataDate, dept_code, dept_name, oacode, runs_stats, start_time, end_time) => {
  const payload = {
    p_request: {
      task_id,
      task_name,
      params: dataDate,
      dept_code,
      dept_name,
      oacode,
      runs_stats,
      start_time,  
      end_time
    }
  }

  try {
    const response = await http.post('/rpc/add_task_log', payload, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Profile': 'mkt_mang'
      }
    })

    const { o_status, o_msg } = response.data || {}

    if (o_status !== 0) {
      throw new Error(o_msg || '添加任务日志失败')
    }

    return true
  } catch (error) {
    console.error('添加任务日志失败:', error)
    ElMessage.error('添加任务日志失败')
    return false
  }
}

// 确认执行
const confirmExecute = async () => {
  if (!executeForm.dataDate) {
    ElMessage.warning('请选择执行日期')
    return
  }

  if (isExecuting.value) {
    ElMessage.warning('任务正在执行中，请勿重复操作')
    return
  }

  isExecuting.value = true

  const deptCode = '1021'
  const dataDate = executeForm.dataDate
  const taskId = currentRow.value.task_id
  const taskName = currentRow.value.task_name
  const taskAction = currentRow.value.action
  const deptName = currentRow.value.dept_name || '默认部门'
  const oa_code = oacode.value

  const runsStats = 'R' // 正在运行
  const startTime = dayjs().format('YYYY-MM-DD HH:mm:ss.SSS') // 开始时间

  try {
    // 1. 执行前：修改任务状态
    const modified = await modifyTaskStatus(taskId, dataDate, oa_code)
    if (!modified) {
      return
    }

    // 2. 执行任务
    const taskNameArray = taskAction.split('.')

    if (taskNameArray.length > 0) { 
      const apiurl = taskNameArray.length == 1 ? taskNameArray[0] : taskNameArray[1]
      const response = await http.post('/rpc/' + apiurl, {
       
        p_data_date: dataDate,
        p_dept_code: deptCode
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Content-Profile': taskNameArray.length == 1 ? 'public' : taskNameArray[0]
        }
      })

      const { o_status, o_msg } = response.data || {}

      // 获取结束时间
      const endTime = dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
  

      if (o_status === 0) {
        // 3. 执行完成后：添加日志（成功）
        await addTaskLog(taskId, taskName, dataDate, deptCode, deptName, oa_code, 'S', startTime, endTime) // 2 表示已完成

        ElMessage.success(`任务已完成`)
        executeDialogVisible.value = false
        handleQuery()
      } else {
        // 4. 如果执行失败，记录日志为失败状态
        await addTaskLog(taskId, taskName, dataDate, deptCode, deptName, oa_code, 'F', startTime, null) // 结束时间为 null

        ElMessage.error(o_msg || `执行任务失败`)
      }
    }
   
  } catch (error) {
    // 5. 捕获异常，记录日志为失败状态
    const endTime = dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
    await addTaskLog(taskId, taskName, dataDate, deptCode, deptName, oacode.value, 'F', startTime, endTime)

    console.error('执行任务失败:', error)
    ElMessage.error('任务执行失败')
  } finally {
    isExecuting.value = false
  }
}

// 查看记录
const handleViewRecord = (row) => {
  const taskId = row.task_id
  router.push({
    path: '/branch-kpi/data-check-cal/etl-task-his',  // 对应 etl-task-his.vue 的路由路径
    query: { task_id: taskId }  // 携带 task_id 参数
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

// 标签显示
const statusLabel = (status) => {
  switch (status) {
    case 'S': return '成功'
    case 'F': return '失败'
    default: return '未知'
  }
}

const statusTagType = (status) => {
  switch (status) {
    case 'S': return 'success'
    case 'F': return 'danger'
    default: return ''
  }
}

const taskTypeLabel = (type) => {
  switch (type) {
    case '0': return '业务跑批'
    case '1': return '数据采集'
    default: return '未知'
  }
}

const systemTypeLabel = (type) => {
  switch (type) {
    case 'exam': return '分支机构考核'
    case 'perf': return '绩效运用'
    default: return '未知'
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>