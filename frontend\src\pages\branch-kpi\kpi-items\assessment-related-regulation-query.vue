<template>
  <div class="report-title">
    <h2>考核相关制度查询</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="制度类型">
              <el-select
                v-model="queryForm.regTypes"
                multiple
                clearable
                placeholder="请选择制度类型"
              >
                <el-option
                  v-for="item in regTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        style="width: 100%"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="reg_type_name" label="制度类型" />
        <el-table-column prop="reg_name" label="制度名称" />
        <el-table-column prop="file_name" label="文件名">
          <template #default="scope">
            <el-link type="primary" @click="handleView(scope.row)" v-if="scope.row.file_name">
              {{ scope.row.file_name }}
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

      <el-dialog v-model="previewDialogVisible" :title="currentRow?.file_name ? currentRow.file_name.slice(0, 20) + (currentRow.file_name.length > 20 ? '...' : '') : '文件预览'" width="80%">
        <!-- <div v-html="previewHtml"></div> -->
        <div ref="previewContainer" class="docx-preview-container"></div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="previewDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="handleDownload(currentRow)">下载</el-button>
          </span>
        </template>
      </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'
import * as docx from 'docx-preview'

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  regTypes: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 制度类型选项
const regTypeOptions = ref([])
const regTypeLoading = ref(false)

// 文件预览对话框
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const previewError = ref('')
const currentRow = ref(null)
const previewContainer = ref(null)

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取制度类型选项
const loadRegTypeOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0005'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    regTypeOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取制度类型选项失败:', error)
    regTypeOptions.value = []
    ElMessage.warning('获取制度类型选项失败')
  }
}

// 初始化数据
onMounted(() => {
  loadRegTypeOptions()
  
  // 设置默认日期：统计开始日期为近两年，统计截止日期为上个月
  const now = new Date();
  
  // 统计截止日期为上个月
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  queryForm.endDate = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`;
  
  // 统计开始日期为近两年（当前日期的两年前）
  const twoYearsAgo = new Date(now.getFullYear() - 2, now.getMonth(), 1);
  queryForm.startDate = `${twoYearsAgo.getFullYear()}-${String(twoYearsAgo.getMonth() + 1).padStart(2, '0')}`;
  
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}

    // 添加日期范围查询条件
    if (queryForm.startDate && queryForm.endDate) {
      // 查询条件：统计开始日期和统计截止日期包含数据中的生效开始日期和生效结束日期
      filters.and = `(tect_strt_date.lte.${queryForm.endDate},tect_end_date.gte.${queryForm.startDate})`
    } else if (queryForm.startDate) {
      filters.tect_end_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.tect_strt_date = `lte.${queryForm.endDate}`
    }

    // 添加制度类型查询条件
    if (queryForm.regTypes && queryForm.regTypes.length > 0) {
      const regTypesStr = queryForm.regTypes.map(type => `"${type}"`).join(',')
      filters.reg_type = `in.(${regTypesStr})`
    }

    // 添加last_flag=1条件
    // filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_exam_rela_reg_mtc', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加日期范围查询条件
    if (queryForm.startDate && queryForm.endDate) {
      // 查询条件：统计开始日期和统计截止日期包含数据中的生效开始日期和生效结束日期
      filters.and = `(tect_strt_date.lte.${queryForm.endDate},tect_end_date.gte.${queryForm.startDate})`
    } else if (queryForm.startDate) {
      filters.tect_end_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.tect_strt_date = `lte.${queryForm.endDate}`
    }

    // 添加制度类型查询条件
    if (queryForm.regTypes && queryForm.regTypes.length > 0) {
      const regTypesStr = queryForm.regTypes.map(type => `"${type}"`).join(',')
      filters.reg_type = `in.(${regTypesStr})`
    }

    // 添加last_flag=1条件
    // filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_exam_rela_reg_mtc', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 为每条记录添加制度类型名称显示
    const exportData = allData.map(item => {
      const regTypeOption = regTypeOptions.value.find(option => option.value === item.reg_type)
      return {
        ...item,
        reg_type_name: regTypeOption ? regTypeOption.label : item.reg_type
      }
    })

    // 准备导出数据
    const excelData = [
      // 表头
      ['生效开始日期', '生效结束日期', '制度类型', '制度名称', '文件名', '创建时间', '更新时间'],
      // 数据行
      ...exportData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.reg_type_name || '',
        item.reg_name || '',
        item.file_name || '',
        formatDateTime(item.crt_time),
        formatDateTime(item.upd_time)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 制度类型
      { wch: 30 }, // 制度名称
      { wch: 30 }, // 文件名
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '考核相关制度查询')

    // 生成文件名
    const now = new Date()
    const fileName = `考核相关制度查询_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}
// 查看详情
const handleView = async (row) => {
  currentRow.value = row
  previewDialogVisible.value = true
  previewLoading.value = true
  previewError.value = ''

  if (row.file_content) {
    try {
      // 提取 Base64 数据
      const base64Data = row.file_content.split(',')[1] || row.file_content

      // 解码 Base64 到 Uint8Array
      const binaryString = atob(base64Data)
      const len = binaryString.length
      const bytes = new Uint8Array(len)
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }

      // 创建 ArrayBuffer
      const arrayBuffer = bytes.buffer

      // 等待 DOM 更新
      await nextTick()

      const container = previewContainer.value
      if (!container) {
        throw new Error('预览容器未找到')
      }

      container.innerHTML = '' // 清空旧内容

      // 使用 docx-preview 渲染文档
      await docx.renderAsync(arrayBuffer, container, null, {
        className: "docx",
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        useMathMLPolyfill: false,
        showChanges: false,
        debug: false
      })

    } catch (error) {
      console.error('文件预览失败:', error)
      previewError.value = '文件预览失败，可能是文件格式不支持或文件已损坏'
    } finally {
      previewLoading.value = false
    }
  } else {
    previewError.value = '文件内容为空'
    previewLoading.value = false
  }
}


// 下载文件
const handleDownload = (row) => {
  const base64Data = row.file_content.split(',')[1] || row.file_content;
  const fileName = row.file_name;

  // Decode Base64 to binary string
  const binaryString = atob(base64Data);

  // Convert binary string to Uint8Array
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob from the Uint8Array
  const blob = new Blob([bytes], { type: 'application/msword' }); // 假设是 Word 文档

  // Create a download link
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;

  // Append the link to the document and trigger the download
  document.body.appendChild(link);
  link.click();

  // Remove the link from the document
  document.body.removeChild(link);
};


// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style lang="scss" scoped>
/* empty */
</style>
