<template>
  <div class="report-title">
    <h2>初始财务报表数据录入</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核结果">
              <el-select
                  v-model="queryForm.audt_relt"
                  placeholder="请选择审核结果"
                  clearable
              >
                <el-option label="未处理" value="0" />
                <el-option label="通过" value="1" />
                <el-option label="不通过" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleDownloadTemplate">下载模板</el-button>
              <el-button type="primary" @click="handleImportData">数据导入</el-button>
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="数据日期"  />
        <el-table-column prop="rptl" label="报表"  />
        <el-table-column prop="index" label="指标"  />
        <el-table-column prop="brh_sum" label="分支机构合计" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.brh_sum) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hs" label="洪山营业部" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hs) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_jh" label="江汉营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_jh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hshi" label="黄石营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hshi) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_qk" label="硚口营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_qk) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sh" label="上海分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sh_mkt4" label="上海分公司市场四部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sh_mkt4) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_bj" label="北京建国门营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_bj) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_gz" label="广州分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_gz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_cd" label="成都营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_cd) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_shbjxl" label="上海北京西路" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_shbjxl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_wc" label="武昌营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_wc) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_cs" label="长沙营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_cs) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_fz" label="福州营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_fz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sz" label="深圳分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_bjhdq" label="北京海淀区营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_bjhdq) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_ty" label="太原营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_ty) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hz" label="杭州营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_shzyl" label="上海张杨路营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_shzyl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_zy" label="中原分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_zy) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_wh" label="武汉分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_wh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_dl" label="大连营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_dl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_yc" label="延长营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_yc) }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人"  />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_oper" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column prop="audt_relt" label="审核结果" >
          <template #default="scope">
            <el-tag
              v-if="scope.row.audt_relt !== null && scope.row.audt_relt !== undefined"
              :type="getAuditStatusTagType(scope.row.audt_relt)"
              size="small"
            >
              {{ formatAuditResult(scope.row.audt_relt) }}
            </el-tag>
            <span v-else>未处理</span>
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_resn" label="审核不通过原因" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="数据导入"
      width="500px"
      @close="handleImportDialogClose"
    >
      <div class="import-content">
        <el-form label-width="100px">
          <el-form-item label="数据日期" required>
            <el-date-picker
              v-model="importForm.dataDate"
              type="month"
              placeholder="选择数据月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          :on-change="handleFileChange"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button class="import-confirm-btn" @click="handleImportConfirm" :loading="importLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { format } from 'date-fns';
import {now} from "@vueuse/core";
import { formatNumber } from '~/utils/format'
import http from '~/http/http.js'

const router = useRouter()
const route = useRoute()

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  order: '',
  audt_relt:''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])
const dictOptions = ref([])

// 导入对话框
const importDialogVisible = ref(false)
const uploadRef = ref()
const fileList = ref([])
const importLoading = ref(false)

// 导入表单
const importForm = reactive({
  dataDate: ''
})

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  queryForm.startDate = params.get('data_date') || ''
  queryForm.endDate = params.get('data_date') || ''
  return {
    oacode: params.get('oacode') || 'current_user',  // 修正：从 URL 中获取 oacode 参数
    roleid: params.get('roleid') || '001',
    goBack: params.get('goback') || false,

  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化审核结果
const formatAuditResult = (result) => {
  // 添加空值处理逻辑
  if (result === null || result === undefined || result === '') {
    return '待审核';
  }
  switch (result) {
    case '0':
    case 0:
      return '未处理'
    case '1':
    case 1:
      return '通过'
    case '2':
    case 2:
      return '不通过'
    default:
      return '未处理'
  }
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status) => {
  switch (status) {
    case '1':
    case 1:
      return 'success'
    case '2':
    case 2:
      return 'danger'
    case '0':
    case 0:
    default:
      return 'warning'
  }
}

// 初始化数据
onMounted(async () => {
  await loadDictList()
  handleQuery()
})

//获取字典
const loadDictList = async () => {
  const filters = {};
  filters.dict_code = `eq.zxkh0015`
  const config = {
    headers: {
      "Accept-Profile": "mkt_base",
    },
    params: {
      ...filters,
    },
  };
  http.get("/v_dictionary_cfg", {}, config).then((response) => {
    const data = response.data;
    dictOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  });
};

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}
    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`
    }
    if (queryForm.audt_relt) {
      filters.audt_relt = `eq.${queryForm.audt_relt}`
    }

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: queryForm.order || 'data_date.desc,sort_no.asc,batch_num.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Content-Profile': 'mkt_base'
      }
    }

  http.get('/audt_init_fin_rptl', {}, config)
    .then(response => {
      tableData.value = response.data || []
      tableData.value.forEach(item=>{
        const dictItem = dictOptions.value.find(dict=>dict.value === item.rptl)
        item.rptl = dictItem ? dictItem.label : item.rptl
      })
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 下载模板
const handleDownloadTemplate = () => {
  // 创建模板数据
  const templateHeaders = [
    '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
    '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部',
    '上海北京西路', '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部',
    '太原营业部', '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部'
  ]

  // 创建示例数据
  const templateData = [
    templateHeaders,
    // ['利润表', '一、收入', 1000, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121],
    // ['利润表', '1、经纪业务收入', 800, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101]
  ]

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(templateData)

  // 设置列宽
  const colWidths = templateHeaders.map(() => ({ wch: 15 }))
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '财务报表数据模板')

  // 下载文件
  XLSX.writeFile(wb, '财务报表数据导入模板.xlsx')

  ElMessage.success('模板下载成功')
}

// 数据导入
const handleImportData = () => {
  importDialogVisible.value = true
  fileList.value = []
  importForm.dataDate = ''
}

// 文件选择变化
const handleFileChange = (file) => {
  fileList.value = [file]
}

// 导入确认
const handleImportConfirm = async () => {
  // 验证数据日期
  if (!importForm.dataDate) {
    ElMessage.warning('请选择数据日期')
    return
  }

  // 验证文件
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importLoading.value = true

  try {
    // 读取Excel文件
    const file = fileList.value[0].raw
    const arrayBuffer = await file.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })

    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // 转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length < 2) {
      ElMessage.error('Excel文件数据格式不正确')
      return
    }

    // 获取表头和数据
    const headers = jsonData[0]
    const dataRows = jsonData.slice(1)

    // 验证表头格式
    const expectedHeaders = [
      '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
      '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部',
      '上海北京西路', '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部',
      '太原营业部', '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部'
    ]

    // 检查表头是否匹配
    const headerMatches = expectedHeaders.every((header, index) => headers[index] === header)
    if (!headerMatches) {
      ElMessage.error('Excel文件表头格式不正确，请使用标准模板')
      return
    }

    // 生成批次号（时间戳）
    const batchNum = Date.now().toString()
    const currentTime = format(now(), 'yyyy-MM-dd HH:mm:ss')

    // 转换数据格式
    const importData = dataRows.map((row, sortNo)=> ({
      batch_num: batchNum,
      data_date: importForm.dataDate,
      rptl: dictOptions.value.find(dict=>dict.label === row[0]).value,
      index: row[1] || '',
      brh_sum: parseFloat(row[2]) || 0,
      brh_hs: parseFloat(row[3]) || 0,
      brh_jh: parseFloat(row[4]) || 0,
      brh_hshi: parseFloat(row[5]) || 0,
      brh_qk: parseFloat(row[6]) || 0,
      brof_sh: parseFloat(row[7]) || 0,
      brof_sh_mkt4: parseFloat(row[8]) || 0,
      brh_bj: parseFloat(row[9]) || 0,
      brof_gz: parseFloat(row[10]) || 0,
      brh_cd: parseFloat(row[11]) || 0,
      brh_shbjxl: parseFloat(row[12]) || 0,
      brh_wc: parseFloat(row[13]) || 0,
      brh_cs: parseFloat(row[14]) || 0,
      brh_fz: parseFloat(row[15]) || 0,
      brof_sz: parseFloat(row[16]) || 0,
      brh_bjhdq: parseFloat(row[17]) || 0,
      brh_ty: parseFloat(row[18]) || 0,
      brh_hz: parseFloat(row[19]) || 0,
      brh_shzyl: parseFloat(row[20]) || 0,
      brof_zy: parseFloat(row[21]) || 0,
      brof_wh: parseFloat(row[22]) || 0,
      brh_dl: parseFloat(row[23]) || 0,
      brh_yc: parseFloat(row[24]) || 0,
      sort_no: sortNo+1,
      creator: urlParams.oacode,
      crt_time: currentTime
    }))

    // 调用API导入数据
    const response = await http.post('/temp_init_fin_rptl', JSON.stringify(importData), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    })

    // 修改：直接检查响应数据中的 o_status 字段
    if (response.data && response.data.o_status === 0) {
      // 数据导入成功后，调用系统采集数据审核
      try {
        const auditResponse = await http.post('/rpc/check_data', {
          p_imp_table: "temp_init_fin_rptl",
          p_audt_table: "audt_init_fin_rptl",
          p_batch_num: batchNum,
          p_task_desc: "初始财务报表数据录入",
          p_url: '/branch-kpi/manual-entry/financial-data-entry' // 使用动态文件名
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Content-Profile': 'mkt_base'
          }
        })

        if (auditResponse.data && auditResponse.data.o_status === 0) {
          ElMessage.success(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表成功`)
        } else if (auditResponse.data && auditResponse.data.o_status === -1) {
          ElMessage.error(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表失败: ${auditResponse.data.o_msg}`)
        } else {
          ElMessage.warning(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表状态未知: ${auditResponse.data?.o_msg || '未知错误'}`)
        }
      } catch (auditError) {
        console.error('系统审核调用失败:', auditError)
        ElMessage.warning(`数据导入成功，但系统审核调用异常`)
      }

      importDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(`数据导入失败: ${response.data?.o_msg || '未知错误'}`)
    }

  } catch (error) {
    console.error('导入数据时发生错误:', error)
    ElMessage.error(`数据导入失败: ${error.response?.data?.o_msg || error.message || '请检查文件格式'}`)
  } finally {
    importLoading.value = false
  }
}

// 导入对话框关闭
const handleImportDialogClose = () => {
  fileList.value = []
  importForm.dataDate = ''
  importLoading.value = false
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`
    }
    if (queryForm.audt_relt) {
      filters.audt_relt = `eq.${queryForm.audt_relt}`
    }

    const config = {
      params: {
        ...filters,
        order: queryForm.order || 'data_date.desc,sort_no.asc,batch_num.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/audt_init_fin_rptl', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '数据日期', '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
        '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部', '上海北京西路',
        '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部', '太原营业部',
        '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部',
        '创建人', '创建时间', '审核人', '审核时间', '审核结果', '审核不通过原因'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date || '',
        dictOptions.value.find(dict=>dict.value === item.rptl).label,
        item.index || '',
        formatNumber(item.brh_sum),
        formatNumber(item.brh_hs),
        formatNumber(item.brh_jh),
        formatNumber(item.brh_hshi),
        formatNumber(item.brh_qk),
        formatNumber(item.brof_sh),
        formatNumber(item.brof_sh_mkt4),
        formatNumber(item.brh_bj),
        formatNumber(item.brof_gz),
        formatNumber(item.brh_cd),
        formatNumber(item.brh_shbjxl),
        formatNumber(item.brh_wc),
        formatNumber(item.brh_cs),
        formatNumber(item.brh_fz),
        formatNumber(item.brof_sz),
        formatNumber(item.brh_bjhdq),
        formatNumber(item.brh_ty),
        formatNumber(item.brh_hz),
        formatNumber(item.brh_shzyl),
        formatNumber(item.brof_zy),
        formatNumber(item.brof_wh),
        formatNumber(item.brh_dl),
        formatNumber(item.brh_yc),
        item.creator || '',
        item.crt_time,
        item.audt_oper || '',
        item.audt_time,
        formatAuditResult(item.audt_relt),
        item.audt_no_pass_resn || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 报表
      { wch: 25 }, // 指标
      ...Array(23).fill({ wch: 15 }), // 各营业部数据
      { wch: 12 }, // 创建人
      { wch: 20 }, // 创建时间
      { wch: 12 }, // 审核人
      { wch: 20 }, // 审核时间
      { wch: 12 }, // 审核结果
      { wch: 25 }  // 审核不通过原因
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '初始财务报表数据录入')

    // 生成文件名
    const now = new Date()
    const fileName = `初始财务报表数据录入_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

</script>

<style lang="scss" scoped>

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
  height: 180px;
}
</style>
