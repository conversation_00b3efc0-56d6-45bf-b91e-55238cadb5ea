<template>
  <div class="report-title">
    <h2>系统采集数据审核</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form
        :model="queryForm"
        class="query-form"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="data_date" label="数据日期" />
        <el-table-column prop="source_system" label="源系统" />
        <el-table-column prop="source_report_name" label="源系统报表名称" />
        <el-table-column prop="audt_department" label="指标审核部门" />
        <el-table-column prop="etl_time" label="采集时间" />
        <el-table-column prop="audtor" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column prop="audt_relt" label="审核结果" >
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.audt_relt).type">
              {{ statusTagType(row.audt_relt).label}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作"  width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <!-- 只有在状态为 "待审核" 时才显示审核按钮 -->
            <el-button
              v-if="scope.row.audt_relt === '0' || scope.row.audt_relt === '' || !scope.row.audt_relt"
              type="success"
              size="small"
              @click="handleAudit(scope.row, 'pass')"
            >
              审核通过
            </el-button>
            <el-button
              v-if="scope.row.audt_relt === '0' || scope.row.audt_relt === '' || !scope.row.audt_relt"
              type="danger"
              size="small"
              @click="handleAudit(scope.row, 'reject')"
            >
              审核不通过
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="dialogTitle"
      width="30%"
      @close="closeAuditDialog"
    >
      <p>确定要将  {{ currentRow.data_date }}的 "{{ currentRow.source_system }}-{{ currentRow.source_report_name }}" 的采集数据设置为 "{{ auditActionText }}" 吗？</p>

      <!-- 审核不通过时显示原因输入框 -->
      <el-form label-position="top" v-if="action === 'reject'">
        <el-form-item label="请输入不通过原因">
          <el-input v-model="rejectReason" type="textarea" :rows="4" placeholder="请输入审核不通过的原因" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAuditDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="auditLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import * as XLSX from "xlsx";
import http from "~/http/http.js";

const router = useRouter();

// 获取默认日期（上个月）
const getDefaultDate = () => {
  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  return `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`;
};

// 查询表单
const queryForm = reactive({
  startDate: getDefaultDate(),
  endDate: getDefaultDate(),
  order: ''
});

// 分页
const currentPage = ref(1);
const pageSize = ref(50);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 审核相关
const auditDialogVisible = ref(false);
const auditLoading = ref(false);
const currentRow = ref({});
const action = ref('');  // 'pass', 'reject'
const rejectReason = ref('');

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacode: params.get("oacode") || "system",  // 修正：从 URL 中获取 oacode 参数
    roleid: params.get("roleid") || "001",
  };
};

const urlParams = getUrlParams();

// 根据审核状态返回标签类型
const statusTagType = (status) => {
  switch (status) {
    case '1':
      return { label: '已通过', type: 'success' };
    case '2':
      return { label: '未通过', type: 'danger' };
    case '0':
      return { label: '待审核', type: 'warning' };
    default:
      return { label: '待审核', type: 'warning' };
  }
}

// 计算属性
const dialogTitle = computed(() => {
  return `确认${auditActionText.value}？`;
});

const auditActionText = computed(() => {
  if (action.value === 'pass') {
    return '审核通过';
  } else if (action.value === 'reject') {
    return '审核不通过';
  } else {
    return '';
  }
});

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {};

  filters.sub_sys_flag = `in.(0,1)`

  // 添加查询条件
  if (queryForm.startDate && queryForm.endDate) {
    filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`;
  } else if (queryForm.startDate) {
    filters.data_date = `gte.${queryForm.startDate}`;
  } else if (queryForm.endDate) {
    filters.data_date = `lte.${queryForm.endDate}`;
  }

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value;
  const limit = pageSize.value;

  const config = {
    params: {
      ...filters,
      order: queryForm.order || "data_date.desc,etl_time.desc"
    },
    headers: {
      "Accept": "application/json",
      "Range": `${offset}-${offset + limit - 1}`,
      "Accept-Profile": "mkt_mang"
    }
  };

  http.get('/etl_data_audt_list', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
};

// 处理审核点击
const handleAudit = (row, auditType) => {
  currentRow.value = { ...row }
  action.value = auditType
  auditDialogVisible.value = true
}

// 关闭审核弹窗
const closeAuditDialog = () => {
  auditDialogVisible.value = false
  action.value = ''
  currentRow.value = {}
  rejectReason.value = ''
}

// 提交审核 - 参考 import-audt.vue 的实现
const handleConfirm = async () => {
  try {
    let audtOper = '';
    if (action.value === 'pass') {
      audtOper = '';
    } else if (action.value === 'reject') {
      audtOper = rejectReason.value;

      if (!rejectReason.value.trim()) {
        ElMessage.warning('请填写审核不通过的原因')
        return;
      }
    }

    auditLoading.value = true;

    const payload = {
      p_audt_table: currentRow.value.report_table, // 表名
      p_data_date: currentRow.value.data_date, // 使用 data_date 作为批次号
      p_audt_oper: urlParams.oacode, // 审核人
      p_audt_no_pass_resn: audtOper, // 不通过原因
    };

    const response = await http.post('/rpc/audt_gather_data', payload, {
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    });

    auditDialogVisible.value = false;

    // 后端返回结构为 { o_status: number, o_msg: string }
    const { o_status, o_msg } = response.data || {};

    if (o_status === 0) {
      ElMessage.success(`${auditActionText.value}成功`);
      rejectReason.value = '';
      handleQuery(); // 刷新列表
    } else {
      ElMessage.error(o_msg || `${auditActionText.value}失败`);
    }

  } catch (error) {
    console.error('审核失败:', error);
    ElMessage.error(`${auditActionText.value}失败`);
  } finally {
    auditLoading.value = false;
  }
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    // 构建查询参数，获取所有数据（不分页）
    const filters = {};

    filters.sub_sys_flag = `in.(0,1)`
    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`;
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`;
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`;
    }

    const config = {
      params: {
        ...filters,
        order: "data_date.desc,etl_time.desc"
      },
      headers: {
        "Accept": "application/json",
        "Accept-Profile": "mkt_mang"
      }
    };

    // 获取所有数据
    const response = await http.get("/etl_data_audt_list", {}, config);
    const allData = response.data || [];

    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "数据日期",
        "源系统",
        "源系统报表名称",
        "指标审核部门",
        "采集时间",
        "审核人",
        "审核时间",
        "审核结果",
        "审核不通过原因",
      ],
      // 数据行
      ...allData.map((item) => [
        item.data_date || "",
        item.source_system || "",
        item.source_report_name || "",
        item.audt_department || "",
        item.etl_time,
        item.audtor || "",
        item.audt_time,
        item.audt_relt || "待审核",
        item.audt_nopass_reason || "",
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 源系统
      { wch: 25 }, // 源系统报表名称
      { wch: 20 }, // 指标审核部门
      { wch: 20 }, // 采集时间
      { wch: 12 }, // 审核人
      { wch: 20 }, // 审核时间
      { wch: 12 }, // 审核结果
      { wch: 25 }, // 审核不通过原因
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "系统采集数据审核");

    // 生成文件名
    const now = new Date();
    const fileName = `系统采集数据审核_${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};

// 查看详情 - 参考 import-audt.vue 的实现
const handleViewDetail = (row) => {
  const reportTable = row.report_table;
  const dataDate = row.data_date;

  // 检查必要字段
  if (!reportTable) {
    ElMessage.error('缺少报表类型信息，无法跳转');
    return;
  }

  if (!dataDate) {
    ElMessage.error('缺少数据日期信息，无法跳转');
    return;
  }

  // 参考 import-audt.vue 的跳转方式
  router.push({
    path: row.url, // 直接使用返回的url字段
    query: {
      oacode: urlParams.oacode,
      data_date: dataDate,
      goback: 'true'
    }
  });
};

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

// 初始化数据
onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>

/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}

/* 对话框样式 */
// .dialog-footer {
//   display: flex;
//   justify-content: flex-end;
//   gap: 10px;
// }
</style>
