<template>
  <div class="report-title">
    <h2>员工信息查询（所有）</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据日期" required>
              <el-date-picker
                v-model="queryForm.dataDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
              <el-button type="info" @click="goBack">返回</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto">
        <el-table-column prop="data_date" label="数据日期"  />
        <el-table-column prop="badge" label="员工编号"  />
        <el-table-column prop="name" label="员工姓名" />
        <el-table-column prop="aduser" label="OA号" />
        <el-table-column prop="gendername" label="性别名称" />
        <el-table-column prop="birthday" label="出生日期" />
        <el-table-column prop="deptname" label="部门名称" />
        <el-table-column prop="deptlevel1name" label="一级部门" />
        <el-table-column prop="deptlevel2name" label="二级部门" />
        <el-table-column prop="jobname" label="岗位名称" />
        <el-table-column prop="empgradename" label="职级（营销级别）名称" />
        <el-table-column prop="empstatusname" label="员工状态名称" />
        <el-table-column prop="joindate" label="入职日期" />
        <el-table-column prop="workbegindate" label="参加工作日期" />
        <el-table-column prop="earlydate" label="加入集团时间" />
        <el-table-column prop="jobbegindate" label="人员从岗时间" />
        <el-table-column prop="leavedate" label="离职时间" />
        <el-table-column prop="highlevel" label="最高学历" />
        <el-table-column prop="party" label="政治面貌" />
        <el-table-column prop="jointypename" label="人员入职类型" />
        <el-table-column prop="workcityname" label="工作城市名称" />
        <el-table-column prop="cyearadjust" label="司龄调整" />
        <el-table-column prop="wyearadjust" label="工龄调整" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_oper" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column prop="audt_relt" label="审核状态">
          <template #default="scope">
            <el-tag :type="getAuditStatusType(scope.row.audt_relt)">
              {{ getAuditStatusText(scope.row.audt_relt) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_resn" label="审核不通过原因" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

const router = useRouter()
const route = useRoute()

// 获取默认日期（上个月）
const getDefaultDate = () => {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  return `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  dataDate: route.query.data_date || getDefaultDate()
})

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  switch (status) {
    case '1': return 'success'
    case '2': return 'danger'
    default: return 'warning'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status) => {
  switch (status) {
    case '1': return '已通过'
    case '2': return '未通过'
    default: return '待审核'
  }
}

// 查询方法
const handleQuery = async () => {
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择数据日期')
    return
  }

  loading.value = true

  try {
    const config = {
      params: {
        data_date: `eq.${queryForm.dataDate}`,
        order: 'data_date.desc,badge.asc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${(currentPage.value - 1) * pageSize.value}-${currentPage.value * pageSize.value - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/cjzq_employee_info', {}, config)
    tableData.value = response.data || []
    totalCount.value = response.total || 0
  } catch (error) {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 导出方法
const handleExport = async () => {
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择数据日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 分批获取数据以避免网络错误
    let allData = [];
    let offset = 0;
    const limit = 1000; // 每批获取1000条数据
    let hasMoreData = true;

    while (hasMoreData) {
      const config = {
        params: {
          data_date: `eq.${queryForm.dataDate}`,
          order: 'data_date.desc,badge.asc'
        },
        headers: {
          'Accept': 'application/json',
          'Range': `${offset}-${offset + limit - 1}`,
          'Accept-Profile': 'mkt_base'
        }
      }

      const response = await http.get('/cjzq_employee_info', {}, config)
      const batchData = response.data || []

      allData = allData.concat(batchData);

      // 如果当前批次数据少于limit，说明已经获取完所有数据
      if (batchData.length < limit) {
        hasMoreData = false;
      }

      offset += limit;
    }

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '数据日期', '员工编号', '员工姓名', 'OA号', '性别名称', '出生日期', '部门名称',
        '一级部门', '二级部门', '岗位名称', '职级（营销级别）名称', '员工状态名称',
        '入职日期', '参加工作日期', '加入集团时间', '人员从岗时间', '离职时间',
        '最高学历', '政治面貌', '人员入职类型', '工作城市名称', '司龄调整', '工龄调整',
        '创建人', '创建时间', '审核人', '审核时间', '审核状态', '审核不通过原因'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date || '',
        item.badge || '',
        item.name || '',
        item.aduser || '',
        item.gendername || '',
        item.birthday || '',
        item.deptname || '',
        item.deptlevel1name || '',
        item.deptlevel2name || '',
        item.jobname || '',
        item.empgradename || '',
        item.empstatusname || '',
        item.joindate || '',
        item.workbegindate || '',
        item.earlydate || '',
        item.jobbegindate || '',
        item.leavedate || '',
        item.highlevel || '',
        item.party || '',
        item.jointypename || '',
        item.workcityname || '',
        item.cyearadjust || '',
        item.wyearadjust || '',
        item.creator || '',
        item.crt_time || '',
        item.audt_oper || '',
        item.audt_time || '',
        getAuditStatusText(item.audt_relt),
        item.audt_no_pass_resn || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 8 }, { wch: 12 }, { wch: 15 },
      { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 18 }, { wch: 12 }, { wch: 12 }, { wch: 14 },
      { wch: 14 }, { wch: 14 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 14 }, { wch: 14 },
      { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 20 }, { wch: 12 }, { wch: 20 }, { wch: 12 }, { wch: 30 }
    ]
    ws['!cols'] = colWidths

    XLSX.utils.book_append_sheet(wb, ws, '员工信息查询（所有）')

    const now = new Date()
    const fileName = `员工信息查询（所有）_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    XLSX.writeFile(wb, fileName)
    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 初始化数据
onMounted(() => {
  handleQuery()
})
</script>

<style lang="scss" scoped>
// 员工信息（所有）查询页面样式
</style>
