<template>
  <div class="report-title">
    <h2>直销绩效奖金分配-审核</h2>
    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm"  class="query-form"  label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期:">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期:">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分支机构:">
              <el-select
                v-model="queryForm.brh_cd"
                filterable
                remote
                clearable
                reserve-keyword
                :remote-method="loadDeptList">
                <el-option
                  v-for="dot in deptOptions"
                  :key="dot.value"
                  :label="dot.label"
                  :value="dot.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData"
                border
                stripe
                table-layout="auto"
                v-loading="loading">
        <el-table-column prop="data_date" label="日期" />
        <el-table-column prop="brh_cd" label="分支机构代码"/>
        <el-table-column prop="brh_nam" label="分支机构名称"/>
        <el-table-column prop="qua_perf_bonus" label="季度绩效奖金金额" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.qua_perf_bonus) }}
          </template>
        </el-table-column>
        <el-table-column prop="oa_id" label="员工编号"/>
        <el-table-column prop="brok_cd" label="业务人员代码" />
        <el-table-column prop="oa_name" label="员工姓名" />
        <el-table-column prop="oa_lvl" label="岗位名称"/>
        <el-table-column prop="assn_rati" label="分配比例"/>
        <el-table-column prop="assn_amt" label="分配金额"  align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.assn_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="create_time" label="创建时间"/>
        <el-table-column prop="audtor" label="审核人"/>
        <el-table-column prop="audt_time" label="审核时间"/>
        <el-table-column prop="audt_relt" label="审核结果">
          <template #default="scope">
            <el-tag
              v-if="scope.row.audt_relt !== null && scope.row.audt_relt !== undefined"
              :type="getAuditStatusTagType(scope.row.audt_relt)"
              size="small"
            >
              {{ formatAuditResult(scope.row.audt_relt) }}
            </el-tag>
            <span v-else>未处理</span>
          </template>
        </el-table-column>
        <el-table-column prop="audt_nopass_reason" label="审核不通过原因"/>
        <el-table-column label="操作"  >
          <template #default="scope">
            <el-button type="success" size="small" @click="handleEdit(scope.row)">审核通过</el-button>
            <el-button type="danger" size="small" @click="handleEditNo(scope.row)">审核不通过</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >

        <el-form-item label="不通过原因:" prop="audt_nopass_reason">
          <el-input
            type="textarea"
            maxlength="100"
            show-word-limit
            :rows="4"
            placeholder="请输入审核不通过的原因"
            v-model="formData.audt_nopass_reason"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { format } from 'date-fns';
import {now} from "@vueuse/core";
import * as XLSX from 'xlsx';
import {formatNumber, getUrlParams} from "~/utils/format.js";
import http from '~/http/http.js';
import Decimal from 'decimal.js'

// 查询表单
const queryForm = reactive({
  startDate:'',
  endDate:'',
  formattedDate: '',
  brh_cd:''
})

let lastSuccessfulForm = JSON.stringify(queryForm);

const formData = reactive({
  id: '',
  audt_nopass_reason:''
})


// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

const deptOptions = ref([])

const urlParams = getUrlParams()
// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 初始化数据
onMounted(() => {
  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery()
})

// 表单验证规则
const formRules = {
  audt_nopass_reason: [
    { required: true, message: '请输入审核不通过原因', trigger: 'change' }
  ]
}

const loadDeptList = async(query) => {
  const config = {
    headers:{
      'Accept-Profile':'mkt_base'
    },
    params:{
      'brh_nam': 'like.%'+query+'%',
    }
  }
  http.get('/v_find_dim_org_select', {}, config).then(response => {
    const data = response.data
    deptOptions.value = data.map(item => ({
      value: item.brh_cd,
      label: item.brh_nam
    }))
  })
}

// 查询
const handleQuery = () => {

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }
  //处理月份
 /* if (!queryForm.startDate || !queryForm.endDate) {
    ElMessage.error('请选择开始日期和截止日期!')
  }*/
  if (queryForm.endDate) {
    let date = queryForm.endDate;
    let year = date.split('-')[0];
    let month = date.split('-')[1];
    const c = (Math.floor((month -1) / 3 ) + 1) * 3
    queryForm.formattedDate = year+ '-' +c.toString().padStart(2, '0')
  }

  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const filters = {}

  if (queryForm.formattedDate) {
    filters.data_date = `like.*${queryForm.formattedDate}*`
  }
  if (queryForm.brh_cd) {
    filters.brh_cd = `eq.${queryForm.brh_cd}`
  }
  const config = {
    headers:{
      'Accept-Profile':'mkt_mang',
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`,
    },
    params:{
      ...filters,
      'order': 'id'
    }
  }
  http.get('/brh_qua_perf_bons_assn', {}, config).then(response=> {
    tableData.value = response.data
    totalCount.value = response.total || 0
    tableData.value.forEach(item=>{
      if (item.assn_rati) {
        const data = new Decimal(item.assn_rati).times(100).toNumber()
        item.assn_rati =  data +'%'
        if (queryForm.startDate && queryForm.endDate) {
          item.data_date = queryForm.startDate + '~' + queryForm.endDate
        }
      }
    })
  }).catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  }).finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 审核通过
const handleEdit = async (row) => {
  const putData = {
    'id': row.id,
    'audt_relt': '1',
    'audtor': urlParams.oacode,
    'audt_time':format(now(), 'yyyy-MM-dd HH:mm:ss'),
    'audt_nopass_reason': ''
  };
  const response = await http.patch(`brh_qua_perf_bons_assn?id=eq.${row.id}`, putData, {
    headers:{
      'Content-Profile':'mkt_mang'
    }
  })
  if (response.data && response.data.o_status === 0) {
    ElMessage.success("审核通过");
    handleQuery(); // 重新查询数据
  } else {
    ElMessage.error("审核操作失败");
  }
  handleQuery()
}

// 审核不通过
const handleEditNo = (row) => {
  dialogTitle.value = '绩效分配比例审核不通过'
  isEdit.value = true
  resetForm()
  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

// 导出
const handleExport = () => {
  ElMessage.info('正在导出数据，请稍候...')
  const filters = {}

  if (queryForm.formattedDate) {
    filters.data_date = `like.*${queryForm.formattedDate}*`
  }
  if (queryForm.brh_cd) {
    filters.brh_cd = `eq.${queryForm.brh_cd}`
  }
  const config = {
    headers:{
      'Accept-Profile':'mkt_mang',
      'Content-Type': 'application/json'
    },
    params: {
      ...filters
    }
  }
  http.get('brh_qua_perf_bons_assn', {}, config).then(response=> {
        const allData = response.data
        if (allData.length === 0) {
          ElMessage.warning('没有数据可导出')
        }
    // 准备导出数据
    const exportData = [

      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '季度绩效奖金金额', '员工编号',
        '业务人员代码', '员工姓名', '岗位名称','分配比例', '分配金额', '创建人',
          '创建时间',  '审核人',  '审核时间', '审核结果', '审核不通过原因'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date,
        item.brh_cd,
        item.brh_nam ,
        item.qua_perf_bonus ,
        item.oa_id,
        item.brok_cd ,
        item.oa_name,
        item.oa_lvl,
        new Decimal(item.assn_rati).times(100) + '%',
        item.assn_amt,
        item.creator|| '',
        item.create_time,
        item.audtor || '',
        item.audt_time,
        formatAuditResult(item.audt_relt),
        item.audt_nopass_reason || ''
      ])
    ]
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)
    // 设置列宽
    // 设置列宽
    const colWidths = [
      ...Array(25).fill({ wch: 15 }),
    ]
    ws['!cols'] = colWidths
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '直销绩效奖金分配-审核')

    // 生成文件名
    const now = new Date()
    const fileName = `直销绩效奖金分配表-审核_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  const putData = {
    'id': formData.id,
    'audt_nopass_reason': formData.audt_nopass_reason,
    'audt_relt': '2',
    'audtor': urlParams.oacode,
    'audt_time':format(now(), 'yyyy-MM-dd HH:mm:ss')
  };
  if (formData.audt_nopass_reason.length > 100) {
    ElMessage.error("原因过长,请重新输入!")
    return
  }
  const response = await http.patch(`/brh_qua_perf_bons_assn?id=eq.${formData.id}`, putData, {
    headers:{
      'Content-Profile':'mkt_mang'
    }
  })
  if (response.data && response.data.o_status === 0) {
    ElMessage.success("审核完成");
    handleQuery(); // 重新查询数据
  } else {
    ElMessage.error("审核操作失败");
  }
  dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brch_grad: '',
    indx_cd: '',
    indx_name: '',
    exam_indx_wght: ''
  })
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status) => {
  switch (status) {
    case '1':
    case 1:
      return 'success'
    case '2':
    case 2:
      return 'danger'
    case '0':
    case 0:
    default:
      return 'warning'
  }
}

const formatAuditResult = (result) => {
  // 添加空值处理逻辑
  if (result === null || result === undefined || result === '') {
    return '待审核';
  }
  switch (result) {
    case '0':
    case 0:
      return '未处理'
    case '1':
    case 1:
      return '审核通过'
    case '2':
    case 2:
      return '审核不通过'
    default:
      return '未处理'
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style lang="scss" scoped>
/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}
</style>