<template>
  <div class="report-title">
    <h2>基础数据准备情况表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据开始日期" required>
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据结束日期" required>
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据状态">
              <el-select
                v-model="queryForm.dataStatus"
                placeholder="请选择数据状态"
                clearable
              >
                <el-option label="未录入" value="未录入" />
                <el-option label="待审核" value="待审核" />
                <el-option label="已审核" value="已审核" />
                <el-option label="未采集" value="未采集" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto">
        <el-table-column prop="period" label="数据日期" />
        <el-table-column prop="task_source_type" label="数据来源类型" >
          <template #default="scope">
            {{ formatSourceType(scope.row.task_source_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="source_system" label="源系统" />
        <el-table-column prop="report_name" label="报表名称" />
        <el-table-column prop="row_nums" label="数据条目数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.row_nums) }}
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" />
        <el-table-column prop="data_status" label="数据状态" >
          <!-- <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.data_status)"
              size="small"
            >
              {{ formatDataStatus(scope.row.data_status) }}
            </el-tag>
          </template> -->
        </el-table-column>
        <el-table-column prop="creator" label="准备人" />
        <el-table-column prop="auditor" label="审核人" />
        <el-table-column prop="table_en_name" label="表英文名" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 获取上个月日期的函数
const getLastMonth = () => {
  const now = new Date()
  let year = now.getFullYear()
  let month = now.getMonth() // 0-11

  // 上一个月
  if (month === 0) { // 如果当前是1月，上一个月是去年12月
    year = year - 1
    month = 12
  }

  const lastMonth = `${year}-${String(month).padStart(2, '0')}`
  return lastMonth
}

const lastMonth = getLastMonth()

// 查询表单
const queryForm = reactive({
  startDate: lastMonth,
  endDate: lastMonth,
  dataStatus: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 格式化数据来源类型
const formatSourceType = (type) => {
  switch (type) {
    case 'sys':
      return '系统采集'
    case 'manu':
      return '人工录入'
    default:
      return type
  }
}

// 格式化数据状态
const formatDataStatus = (status) => {
  switch (status) {
    case '0':
      return '未采集'
    case '1':
      return '已采集'
    case '2':
      return '未录入'
    case '3':
      return '已录入'
    default:
      return status
  }
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case '3': // 已录入
    case '1': // 已采集
      return 'success'
    case '2': // 未录入
    case '0': // 未采集
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}

    // 添加默认条件：sub_sys_flag in ('0','1')
    filters.sub_sys_flag = `in.(0,2)`

    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(period.gte.${queryForm.startDate},period.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.period = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.period = `lte.${queryForm.endDate}`
    }

    if (queryForm.dataStatus) {
      filters.data_status = `eq.${queryForm.dataStatus}`
    }

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'period.desc,task_id.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_mang'
      }
    }

  http.get('/data_task_check_result', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加默认条件：sub_sys_flag in ('0','1')
    filters.sub_sys_flag = `in.(0,2)`

    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(period.gte.${queryForm.startDate},period.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.period = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.period = `lte.${queryForm.endDate}`
    }

    const config = {
      params: {
        ...filters,
        order: 'period.desc,task_id.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_mang'
      }
    }

    // 获取所有数据
    const response = await http.get('/data_task_check_result', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '数据日期', '数据来源类型', '源系统', '报表名称', '数据条目数',
        '部门', '数据状态', '准备人', '审核人', '表英文名'
      ],
      // 数据行
      ...allData.map(item => [
        item.period,
        formatSourceType(item.task_source_type),
        item.source_system || '',
        item.report_name || '',
        formatNumber(item.row_nums),
        item.department || '',
        formatDataStatus(item.data_status),
        item.creator || '',
        item.auditor || '',
        item.table_en_name || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 数据来源类型
      { wch: 15 }, // 源系统
      { wch: 30 }, // 报表名称
      { wch: 15 }, // 数据条目数
      { wch: 20 }, // 部门
      { wch: 12 }, // 数据状态
      { wch: 12 }, // 准备人
      { wch: 12 }, // 审核人
      { wch: 35 }  // 表英文名
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '基础数据准备情况')

    // 生成文件名
    const now = new Date()
    const fileName = `基础数据准备情况表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style lang="scss" scoped>

/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}

</style>
