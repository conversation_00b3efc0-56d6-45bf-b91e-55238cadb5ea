<template>
  <div class="report-title">
    <h2>日均权益考核得分明细表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.dataDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务人员">
              <el-input
                v-model="queryForm.busiPerson"
                placeholder="请输入业务人员代码或名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗位职级">
              <el-input
                v-model="queryForm.postLevel"
                placeholder="请输入岗位职级"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="busi_prsn_cd" label="业务人员代码" />
        <el-table-column prop="busi_prsn_nam" label="业务人员名称" />
        <el-table-column prop="post_nam" label="岗位名称" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="stay_equi_last_base" label="保有日均权益上年基数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.stay_equi_last_base) }}
          </template>
        </el-table-column>
        <el-table-column prop="curr_brok_equi" label="当期经纪业务日均权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.curr_brok_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="curr_inov_equi" label="当期创新业务折算权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.curr_inov_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="stay_equi" label="当期保有日均权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.stay_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="equi_wght" label="日均权益考核权重" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.equi_wght) }}
          </template>
        </el-table-column>
        <el-table-column prop="stay_equi_wght" label="保有日均权益考核权重" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.stay_equi_wght) }}
          </template>
        </el-table-column>
        <el-table-column prop="stay_equi_scor" label="保有日均权益考核得分" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.stay_equi_scor) }}
          </template>
        </el-table-column>
        <el-table-column prop="nadd_equi_last_base" label="净增日均权益上年12月基数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nadd_equi_last_base) }}
          </template>
        </el-table-column>
        <el-table-column prop="nadd_equi_tgt" label="净增日均权益当期目标" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nadd_equi_tgt) }}
          </template>
        </el-table-column>
        <el-table-column prop="brok_equi" label="当月经纪业务日均权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brok_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="inov_equi" label="当月创新业务折算权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.inov_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="curr_nadd_equi" label="当期净增日均权益" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.curr_nadd_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="nadd_equi_wght" label="净增日均权益考核权重" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nadd_equi_wght) }}
          </template>
        </el-table-column>
        <el-table-column prop="nadd_equi_scor" label="净增日均权益考核得分" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nadd_equi_scor) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期（上个月）
const getDefaultDate = () => {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  return `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  dataDate: getDefaultDate(),
  busiPerson: '',
  postLevel: '',
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

let lastSuccessfulForm = JSON.stringify(queryForm);

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '-'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 获取所有数据（不分页）
const fetchAllData = async () => {
  try {
    loading.value = true

    const functionParams = {
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        srcsys: urlParams.srcsys,
        p_data_date: queryForm.dataDate,
        p_brh_cd: queryForm.branchCode || null,
        p_busi_prsn: queryForm.busiPerson || null,
        p_post_lvl: queryForm.postLevel || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const response = await http.callFunction('p_busi_equi_exam_scor_dtl_s', functionParams, config)
    return response.data || []
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
    return []
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  try {
    const rawData = await fetchAllData()
    
    // 进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  } catch (error) {
    console.error('处理数据时发生错误:', error)
    ElMessage.error('处理数据时发生错误')
  } finally {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  }
}

// 处理导出
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 获取所有数据
    const functionParams = {
      i_request: {
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        srcsys: urlParams.srcsys,
        p_data_date: queryForm.dataDate,
        p_brh_cd: queryForm.branchCode || null,
        p_busi_prsn: queryForm.busiPerson || null,
        p_post_lvl: queryForm.postLevel || null
      }
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const response = await http.callFunction('p_busi_equi_exam_scor_dtl_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '业务人员代码', '业务人员名称', '岗位名称', '岗位职级',
        '保有日均权益上年基数', '当期经纪业务日均权益', '当期创新业务折算权益', '当期保有日均权益',
        '日均权益考核权重', '保有日均权益考核权重', '保有日均权益考核得分', '净增日均权益上年12月基数',
        '净增日均权益当期目标', '当月经纪业务日均权益', '当月创新业务折算权益', '当期净增日均权益',
        '净增日均权益考核权重', '净增日均权益考核得分'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date ?? '',
        item.brh_cd ?? '',
        item.brh_nam ?? '',
        item.busi_prsn_cd ?? '',
        item.busi_prsn_nam ?? '',
        item.post_nam ?? '',
        item.post_lvl ?? '',
        formatNumber(item.stay_equi_last_base),
        formatNumber(item.curr_brok_equi),
        formatNumber(item.curr_inov_equi),
        formatNumber(item.stay_equi),
        formatNumber(item.equi_wght),
        formatNumber(item.stay_equi_wght),
        formatNumber(item.stay_equi_scor),
        formatNumber(item.nadd_equi_last_base),
        formatNumber(item.nadd_equi_tgt),
        formatNumber(item.brok_equi),
        formatNumber(item.inov_equi),
        formatNumber(item.curr_nadd_equi),
        formatNumber(item.nadd_equi_wght),
        formatNumber(item.nadd_equi_scor)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 业务人员代码
      { wch: 20 }, // 业务人员名称
      { wch: 15 }, // 岗位名称
      { wch: 12 }, // 岗位职级
      { wch: 20 }, // 保有日均权益上年基数
      { wch: 20 }, // 当期经纪业务日均权益
      { wch: 20 }, // 当期创新业务折算权益
      { wch: 15 }, // 当期保有日均权益
      { wch: 15 }, // 日均权益考核权重
      { wch: 20 }, // 保有日均权益考核权重
      { wch: 20 }, // 保有日均权益考核得分
      { wch: 22 }, // 净增日均权益上年12月基数
      { wch: 20 }, // 净增日均权益当期目标
      { wch: 20 }, // 当月经纪业务日均权益
      { wch: 20 }, // 当月创新业务折算权益
      { wch: 18 }, // 当期净增日均权益
      { wch: 20 }, // 净增日均权益考核权重
      { wch: 20 }  // 净增日均权益考核得分
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '日均权益考核得分明细表')

    // 生成文件名
    const now = new Date()
    const fileName = `日均权益考核得分明细表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

// 初始化加载数据
onMounted(() => {
  handleQuery()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>