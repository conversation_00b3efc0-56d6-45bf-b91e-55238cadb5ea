<template>
  <div class="report-title">
    <h2>营销人员新增有效户统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="投资者代码">
              <el-input
                v-model="queryForm.investorCode"
                placeholder="请输入投资者代码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="投资者名称">
              <el-input
                v-model="queryForm.investorName"
                placeholder="请输入投资者名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="业务人员">
              <el-select
                v-model="queryForm.businessPersonCode"
                filterable
                remote
                reserve-keyword
                placeholder="请输入业务人员穿透代码或名称进行搜索"
                :remote-method="remoteSearchBusinessPerson"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in businessPersonOptions"
                  :key="item.value"
                  :label="`${item.value} - ${item.label}`"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="机构归属" required>
              <el-select
                v-model="queryForm.orgBelong"
                placeholder="请选择机构归属"
              >
                <el-option label="历史归属" value="historical" />
                <el-option label="最新归属" value="latest" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="valid_cust_date" label="有效户归属日期" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="ivst_cd" label="投资者代码" />
        <el-table-column prop="ivst_name" label="投资者名称" />
        <el-table-column prop="ivst_type" label="投资者类型" />
        <el-table-column prop="dev_mode" label="开发方式" />
        <el-table-column prop="emp_cd" label="业务人员穿透代码" />
        <el-table-column prop="emp_name" label="业务人员穿透名称" />
        <el-table-column prop="actl_perf_pct" label="实际业绩占比" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.actl_perf_pct) }}
          </template>
        </el-table-column>
        <el-table-column prop="openacct_dt" label="开户日期" />
        <el-table-column prop="active_dt" label="激活日期" />
        <el-table-column prop="nadd_eff_cust" label="净增有效户数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nadd_eff_cust) }}
          </template>
        </el-table-column>
        <el-table-column prop="eff_cust_appd" label="追加折算有效户数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.eff_cust_appd) }}
          </template>
        </el-table-column>
        <el-table-column prop="active_eff_cust" label="休眠激活有效户数" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.active_eff_cust) }}
          </template>
        </el-table-column>
        <el-table-column prop="eff_cust_cnvr" label="有效户合计" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.eff_cust_cnvr) }}
          </template>
        </el-table-column>
        <el-table-column prop="assn_aft_effh_sum" label="分配后有效户合计" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.assn_aft_effh_sum) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')
const roleid = ref(route.query.roleid || '0004')

// 获取默认日期
const getDefaultDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11

  // 当前系统时间上一个月份
  let endYear = currentYear
  let endMonth = currentMonth // 当前月份的上一个月

  if (endMonth === 0) { // 如果当前是1月，上一个月是去年12月
    endYear = currentYear - 1
    endMonth = 12
  }

  const endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`

  return { endDate }
}

const defaultDates = getDefaultDates()

// 查询表单
const queryForm = reactive({
  endDate: defaultDates.endDate,
  branchCode: '',
  investorCode: '',
  investorName: '',
  businessPersonCode: '',
  orgBelong: 'historical'
})

// 业务人员选项
const businessPersonOptions = ref([])

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 远程搜索业务人员
const remoteSearchBusinessPerson = async (query) => {
  if (query) {
    try {
      // 构建查询参数
      const config = {
        params: {
          or: `(broker_id.ilike.*${query}*,broker_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Content-Profile': 'mkt_mang'
        }
      }

      const response = await http.get('/v_mkt_emp_info', {}, config)
      const data = response.data || []

      // 去重并转换为选项格式
      const uniquePersons = new Map()
      data.forEach(item => {
        if (item.broker_id && item.broker_nam) {
          uniquePersons.set(item.broker_id, {
            value: item.broker_id,
            label: item.broker_nam
          })
        }
      })
      
      businessPersonOptions.value = Array.from(uniquePersons.values())
        .sort((a, b) => a.value.localeCompare(b.value))
    } catch (error) {
      console.error('搜索业务人员失败:', error)
      businessPersonOptions.value = []
    }
  } else {
    // 如果没有查询条件，加载所有业务人员
    loadAllBusinessPersons()
  }
}

// 加载所有业务人员
const loadAllBusinessPersons = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_mang'
      }
    }

    const response = await http.get('/v_mkt_emp_info', {}, config)
    const data = response.data || []

    // 去重并转换为选项格式
    const uniquePersons = new Map()
    data.forEach(item => {
      if (item.broker_id && item.broker_nam) {
        uniquePersons.set(item.broker_id, {
          value: item.broker_id,
          label: item.broker_nam
        })
      }
    })
    
    businessPersonOptions.value = Array.from(uniquePersons.values())
      .sort((a, b) => a.value.localeCompare(b.value))
  } catch (error) {
    console.error('加载业务人员失败:', error)
    businessPersonOptions.value = []
  }
}

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  try {
    const response = await http.post(
      '/rpc/p_mngr_new_valid_cust_stat_s',
      {
        i_request: {
          optionflg: '1',
          srcsys: srcsys.value,
          oacode: oacode.value,
          roleid: roleid.value,
          p_start_date: queryForm.endDate.substring(0, 4) + '-01', // 当年1月
          p_end_date: queryForm.endDate,
          p_brh_cd: queryForm.branchCode || '',
          p_ivst_cd: queryForm.investorCode || '',
          p_ivst_name: queryForm.investorName || '',
          p_busi_prsn_cd: queryForm.businessPersonCode || '',
          p_org_belong: queryForm.orgBelong
        }
      },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data) {
      // 确保 response.data 是数组类型
      const data = Array.isArray(response.data) ? response.data : []

      // 由于函数已经进行了汇总，直接使用返回的数据
      // 但需要进行分页处理
      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      const paginatedData = data.slice(startIndex, endIndex)

      tableData.value = paginatedData
      totalCount.value = data.length
    } else {
      tableData.value = []
      totalCount.value = 0
      ElMessage.info('未找到相关数据')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请检查网络连接')
    tableData.value = []
    totalCount.value = 0
  } finally {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  }
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    const response = await http.post(
      '/rpc/p_mngr_new_valid_cust_stat_s',
      {
        i_request: {
          optionflg: '1',
          srcsys: srcsys.value,
          oacode: oacode.value,
          roleid: roleid.value,
          p_start_date: queryForm.endDate.substring(0, 4) + '-01', // 当年1月
          p_end_date: queryForm.endDate,
          p_brh_cd: queryForm.branchCode || '',
          p_ivst_cd: queryForm.investorCode || '',
          p_ivst_name: queryForm.investorName || '',
          p_busi_prsn_cd: queryForm.businessPersonCode || '',
          p_org_belong: queryForm.orgBelong
        }
      },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    const exportData = response.data || []

    if (exportData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const excelData = [
      // 表头
      [
        '有效户归属日期', '分支机构代码', '分支机构名称', '投资者代码', '投资者名称', '投资者类型',
        '开发方式', '业务人员穿透代码', '业务人员穿透名称', '实际业绩占比', '开户日期', '激活日期',
        '净增有效户数', '追加折算有效户数', '休眠激活有效户数', '有效户合计', '分配后有效户合计'
      ],
      // 数据行
      ...exportData.map(item => [
        item.valid_cust_date ?? '',
        item.brh_cd ?? '',
        item.brh_nam ?? '',
        item.ivst_cd ?? '',
        item.ivst_name ?? '',
        item.ivst_type ?? '',
        item.dev_mode ?? '',
        item.emp_cd ?? '',
        item.emp_name ?? '',
        formatNumber(item.actl_perf_pct),
        item.openacct_dt ?? '',
        item.active_dt ?? '',
        formatNumber(item.nadd_eff_cust),
        formatNumber(item.eff_cust_appd),
        formatNumber(item.active_eff_cust),
        formatNumber(item.eff_cust_cnvr),
        formatNumber(item.assn_aft_effh_sum)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 18 }, // 有效户归属日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 投资者代码
      { wch: 20 }, // 投资者名称
      { wch: 12 }, // 投资者类型
      { wch: 12 }, // 开发方式
      { wch: 18 }, // 业务人员穿透代码
      { wch: 18 }, // 业务人员穿透名称
      { wch: 15 }, // 实际业绩占比
      { wch: 12 }, // 开户日期
      { wch: 12 }, // 激活日期
      { wch: 15 }, // 净增有效户数
      { wch: 18 }, // 追加折算有效户数
      { wch: 18 }, // 休眠激活有效户数
      { wch: 15 }, // 有效户合计
      { wch: 18 }  // 分配后有效户合计
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '营销人员新增有效户统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `营销人员新增有效户统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${exportData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

// 初始化数据
onMounted(() => {
  handleQuery()
  loadAllBusinessPersons()
})
</script>

<style lang="scss" scoped>
/* empty */
</style>
