<template>
  <div class="report-title">
    <h2>市场部经理维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="市场部经理:">
              <el-input
                v-model="queryForm.managerQuery"
                placeholder="请输入市场部经理代码或名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="mkt_dept_mngr_cd" label="市场部经理代码" />
        <el-table-column prop="mkt_dept_mngr_nam" label="市场部经理名称" />
        <el-table-column prop="post_nam" label="岗位名称" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <el-button
              type="success"
              size="small"
              @click="handleHistory(scope.row)"
              >历史记录</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                :disabled="!isEdit"
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="市场部经理代码" prop="mkt_dept_mngr_cd">
              <el-input
                v-model="formData.mkt_dept_mngr_cd"
                placeholder="请输入市场部经理代码"
                :disabled="isEdit"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位名称" prop="post_nam">
              <el-input
                v-model="formData.post_nam"
                placeholder="请输入岗位名称"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位职级" prop="post_lvl">
              <el-input
                v-model="formData.post_lvl"
                placeholder="请输入岗位职级"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="历史记录" width="1000px">
      <el-table :data="historyData" border stripe table-layout="auto">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="mkt_dept_mngr_cd" label="市场部经理代码" />
        <el-table-column prop="mkt_dept_mngr_nam" label="市场部经理名称" />
        <el-table-column prop="post_nam" label="岗位名称" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as XLSX from "xlsx";
import http from "~/http/http.js";

// 查询表单
const queryForm = reactive({
  managerQuery: "",
});

// 分页
const currentPage = ref(1);
const pageSize = ref(50);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacode: params.get("oacode") || "xurr1",
    roleid: params.get("roleid") || "001",
  };
};

const urlParams = getUrlParams();

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const isEdit = ref(false);

// 历史记录对话框
const historyDialogVisible = ref(false);
const historyData = ref([]);

// 表单数据
const formData = reactive({
  tect_strt_date: "",
  tect_end_date: "",
  mkt_dept_mngr_cd: "",
  post_nam: "",
  post_lvl: "",
  uuid: "",
});

// 原始的开始日期（编辑时保存）
const originalStartDate = ref("");

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: "请选择生效开始日期", trigger: "change" },
  ],
  tect_end_date: [
    { required: true, message: "请选择生效结束日期", trigger: "change" },
  ],
  mkt_dept_mngr_cd: [
    { required: true, message: "请输入市场部经理代码", trigger: "blur" },
  ],
  post_nam: [
    { required: true, message: "请输入岗位名称", trigger: "blur" },
  ],
  post_lvl: [
    { required: true, message: "请输入岗位职级", trigger: "blur" },
  ],
};

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return (
    formData.tect_strt_date &&
    formData.tect_end_date &&
    formData.mkt_dept_mngr_cd &&
    formData.post_nam &&
    formData.post_lvl
  );
});

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value || !isEdit.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};

// 初始化数据
onMounted(() => {
  handleQuery();
});

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {};

  // 添加查询条件
  if (queryForm.managerQuery) {
    // 支持按代码或名称模糊查询
    const query = queryForm.managerQuery.trim();
    filters.or = `(mkt_dept_mngr_cd.ilike.*${query}*,mkt_dept_mngr_nam.ilike.*${query}*)`;
  }

  // 添加last_flag=1条件
  filters.last_flag = "eq.1";

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value;
  const limit = pageSize.value;

  const config = {
    params: {
      ...filters,
      order: "tect_strt_date.desc,crt_time.desc",
    },
    headers: {
      Accept: "application/json",
      Range: `${offset}-${offset + limit - 1}`,
      "Accept-Profile": "mkt_base",
    },
  };

  http
    .get("/v_mkt_dept_mngr_mtc", {}, config)
    .then((response) => {
      tableData.value = response.data || [];
      totalCount.value = response.total || 0;
    })
    .catch((error) => {
      console.error("API请求失败:", error);
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false;
    });
};

// 新增
const handleAdd = () => {
  dialogTitle.value = "新增市场部经理维护";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 修改
const handleEdit = (row) => {
  dialogTitle.value = "修改市场部经理维护";
  isEdit.value = true;
  resetForm();

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(formData, row);

  dialogVisible.value = true;
};

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该市场部经理的所有历史记录
    const config = {
      params: {
        mkt_dept_mngr_cd: `eq.${row.mkt_dept_mngr_cd}`,
        order: "crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get("/v_mkt_dept_mngr_mtc", {}, config);
    const data = response.data || [];

    historyData.value = data;
    historyDialogVisible.value = true;
  } catch (error) {
    console.error("获取历史记录时发生错误:", error);
    ElMessage.error("获取历史记录失败，请检查网络连接");
  }
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    // 构建查询参数，获取所有数据（不分页）
    const filters = {};

    // 添加查询条件
    if (queryForm.managerQuery) {
      const query = queryForm.managerQuery.trim();
      filters.or = `(mkt_dept_mngr_cd.ilike.*${query}*,mkt_dept_mngr_nam.ilike.*${query}*)`;
    }

    // 添加last_flag=1条件
    filters.last_flag = "eq.1";

    const config = {
      params: {
        ...filters,
        order: "tect_strt_date.desc,crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    // 获取所有数据
    const response = await http.get("/v_mkt_dept_mngr_mtc", {}, config);
    const allData = response.data || [];

    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "生效开始日期",
        "生效结束日期",
        "市场部经理代码",
        "市场部经理名称",
        "岗位名称",
        "岗位职级",
        "创建时间",
        "更新时间",
      ],
      // 数据行
      ...allData.map((item) => [
        item.tect_strt_date || "",
        item.tect_end_date || "",
        item.mkt_dept_mngr_cd || "",
        item.mkt_dept_mngr_nam || "",
        item.post_nam || "",
        item.post_lvl || "",
        item.crt_time,
        item.upd_time,
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 15 }, // 市场部经理代码
      { wch: 20 }, // 市场部经理名称
      { wch: 15 }, // 岗位名称
      { wch: 15 }, // 岗位职级
      { wch: 20 }, // 创建时间
      { wch: 20 }, // 更新时间
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "市场部经理维护");

    // 生成文件名
    const now = new Date();
    const fileName = `市场部经理维护_${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacode,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            mkt_dept_mngr_cd: formData.mkt_dept_mngr_cd,
            post_nam: formData.post_nam,
            post_lvl: formData.post_lvl,
          },
        };

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid;
        }

        const response = await http.post(
          "/rpc/p_mkt_dept_mngr_mtc_e",
          requestData,
          {
            headers: {
              "Content-Profile": "mkt_base",
              "Content-Type": "application/json",
            },
          }
        );

        // 检查响应数据中的 o_status 字段
        if (response.data && response.data.o_status === 0) {
          ElMessage.success(isEdit.value ? "修改成功" : "新增成功");
          dialogVisible.value = false;
          handleQuery(); // 重新查询数据
        } else {
          ElMessage.error(response.data?.o_msg || "操作失败");
        }
      } catch (error) {
        console.error("提交数据时发生错误:", error);
        ElMessage.error("操作失败，请检查网络连接");
      }
    }
  });
};

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确认删除该条记录？", "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacode,
        uuid: row.uuid,
      },
    };

    const response = await http.post(
      "/rpc/p_mkt_dept_mngr_mtc_e",
      requestData,
      {
        headers: {
          "Content-Profile": "mkt_base",
          "Content-Type": "application/json",
        },
      }
    );

    // 检查响应数据中的 o_status 字段
    if (response.data && response.data.o_status === 0) {
      ElMessage.success("删除成功");
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除数据时发生错误:", error);
      ElMessage.error("删除失败，请检查网络连接");
    }
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  Object.assign(formData, {
    tect_strt_date: "",
    tect_end_date: "",
    mkt_dept_mngr_cd: "",
    post_nam: "",
    post_lvl: "",
    uuid: "",
  });
};

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};
</script>

<style lang="scss" scoped>
/* empty */
</style>
