<template>
  <div class="report-title">
    <h2>市场部必选指标设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="市场部经理">
              <el-select
                v-model="queryForm.deptMngrCd"
                filterable
                remote
                reserve-keyword
                placeholder="请输入市场部经理代码或名称进行搜索"
                :remote-method="remoteSearchDeptManager"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in deptManagerOptions"
                  :key="item.value"
                  :label="`${item.value} - ${item.label}`"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效开始日期">
              <el-date-picker
                v-model="queryForm.tectStrtDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效结束日期">
              <el-date-picker
                v-model="queryForm.tectEndDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期"  />
        <el-table-column prop="tect_end_date" label="生效结束日期"  />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称"  />
        <el-table-column prop="dept_mngr_cd" label="市场部经理代码"  />
        <el-table-column prop="dept_mngr_nam" label="市场部经理名称" />
        <el-table-column prop="post_nam" label="岗位名称"  />
        <el-table-column prop="post_lvl" label="岗位职级"  />
        <el-table-column prop="stay_equi_last_base" label="保有日均权益上年基数" />
        <el-table-column prop="nadd_equi_last_base" label="净增日均权益上年12月基数"/>
        <el-table-column prop="nadd_equi_tgt" label="净增日均权益月目标" />
        <el-table-column prop="nadd_equi_wght" label="日均权益考核权重" />
        <el-table-column prop="net_incm_tgt" label="净收入月度目标"  />
        <el-table-column prop="net_incm_wght" label="净收入考核权重"  />
        <el-table-column prop="add_val_tgt" label="新增有效户月度目标" />
        <el-table-column prop="add_val_wght" label="新增有效户考核权重" />
        <el-table-column prop="fix_indx_wght" label="必选指标考核权重" />
        <el-table-column prop="dept_pct" label="市场部考核占比" />
        <el-table-column prop="dept_mngr_pct" label="市场部经理个人考核占比" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_prsn" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column prop="audt_relt" label="审核结果">
          <template #default="scope">
            <span v-if="scope.row.audt_relt === '0'">未审核</span>
            <span v-else-if="scope.row.audt_relt === '1'">审核通过</span>
            <span v-else-if="scope.row.audt_relt === '2'">审核不通过</span>
            <span v-else>{{ scope.row.audt_relt }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_reason" label="审核不通过原因" />
        
        <!-- 操作列 -->
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <!-- srcsys=1时显示审核通过、审核不通过、历史记录 -->
            <template v-if="srcsys === '1'">
              <el-button
                type="success"
                size="small"
                @click="handleApprove(scope.row)"
                :disabled="isAuditDisabled(scope.row)"
              >
                审核通过
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleReject(scope.row)"
                :disabled="isAuditDisabled(scope.row)"
              >
                审核不通过
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleHistory(scope.row)"
              >
                历史记录
              </el-button>
            </template>
            
            <!-- srcsys=2时显示修改、历史记录 -->
            <template v-else-if="srcsys === '2'">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                修改
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleHistory(scope.row)"
              >
                历史记录
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 修改对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改市场部必选指标设置"
      width="80%"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="180px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-date-picker
                v-model="editForm.tect_strt_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="editForm.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="市场部经理代码">
              <el-input v-model="editForm.dept_mngr_cd" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保有日均权益上年基数" prop="stay_equi_last_base">
              <el-input-number v-model="editForm.stay_equi_last_base" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="净增日均权益上年12月基数" prop="nadd_equi_last_base">
              <el-input-number v-model="editForm.nadd_equi_last_base" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净增日均权益月目标" prop="nadd_equi_tgt">
              <el-input-number v-model="editForm.nadd_equi_tgt" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日均权益考核权重" prop="nadd_equi_wght">
              <el-input-number v-model="editForm.nadd_equi_wght" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净收入月度目标" prop="net_incm_tgt">
              <el-input-number v-model="editForm.net_incm_tgt" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="净收入考核权重" prop="net_incm_wght">
              <el-input-number v-model="editForm.net_incm_wght" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新增有效户月度目标" prop="add_val_tgt">
              <el-input-number v-model="editForm.add_val_tgt" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="新增有效户考核权重" prop="add_val_wght">
              <el-input-number v-model="editForm.add_val_wght" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="必选指标考核权重" prop="fix_indx_wght">
              <el-input-number v-model="editForm.fix_indx_wght" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="市场部考核占比" prop="dept_pct">
              <el-input-number v-model="editForm.dept_pct" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="市场部经理个人考核占比" prop="dept_mngr_pct">
              <el-input-number v-model="editForm.dept_mngr_pct" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSaveEdit" :loading="editLoading">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核通过确认对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审核通过确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="confirm-content">
        <p>确认要审核通过此记录吗？</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleApproveConfirm" :loading="approveLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核不通过对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核不通过"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef">
        <el-form-item label="不通过原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核不通过的原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRejectConfirm" :loading="rejectLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="历史记录"
      width="90%"
      :close-on-click-modal="false"
    >
      <el-table
        :data="historyData"
        border
        stripe
        table-layout="auto"
        v-loading="historyLoading"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="dept_mngr_cd" label="市场部经理代码" />
        <el-table-column prop="dept_mngr_nam" label="市场部经理名称" />
        <el-table-column prop="post_nam" label="岗位名称" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="stay_equi_last_base" label="保有日均权益上年基数" />
        <el-table-column prop="nadd_equi_last_base" label="净增日均权益上年12月基数" />
        <el-table-column prop="nadd_equi_tgt" label="净增日均权益月目标" />
        <el-table-column prop="nadd_equi_wght" label="日均权益考核权重" />
        <el-table-column prop="net_incm_tgt" label="净收入月度目标" />
        <el-table-column prop="net_incm_wght" label="净收入考核权重" />
        <el-table-column prop="add_val_tgt" label="新增有效户月度目标" />
        <el-table-column prop="add_val_wght" label="新增有效户考核权重" />
        <el-table-column prop="fix_indx_wght" label="必选指标考核权重" />
        <el-table-column prop="dept_pct" label="市场部考核占比" />
        <el-table-column prop="dept_mngr_pct" label="市场部经理个人考核占比" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_prsn" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column prop="audt_relt" label="审核结果">
          <template #default="scope">
            <span v-if="scope.row.audt_relt === '0'">未审核</span>
            <span v-else-if="scope.row.audt_relt === '1'">审核通过</span>
            <span v-else-if="scope.row.audt_relt === '2'">审核不通过</span>
            <span v-else>{{ scope.row.audt_relt }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_reason" label="审核不通过原因" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const route = useRoute()

// 获取URL参数
const oacode = ref(route.query.oacode || 'system')
const roleid = ref(route.query.roleid || '0001')
const srcsys = ref(route.query.srcsys || '1')

// 查询表单
const queryForm = reactive({
  branchCode: '',
  deptMngrCd: '',
  tectStrtDate: '',
  tectEndDate: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 市场部经理选项
const deptManagerOptions = ref([])

// 修改对话框相关
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  dept_mngr_cd: '',
  stay_equi_last_base: null,
  nadd_equi_last_base: null,
  nadd_equi_tgt: null,
  nadd_equi_wght: null,
  net_incm_tgt: null,
  net_incm_wght: null,
  add_val_tgt: null,
  add_val_wght: null,
  fix_indx_wght: null,
  dept_pct: null,
  dept_mngr_pct: null,
  uuid: ''
})

const editRules = {
  tect_strt_date: [{ required: true, message: '请选择生效开始日期', trigger: 'change' }],
  tect_end_date: [{ required: true, message: '请选择生效结束日期', trigger: 'change' }]
}

// 审核通过对话框相关
const approveDialogVisible = ref(false)
const approveLoading = ref(false)
const currentRow = ref(null)

// 审核不通过对话框相关
const rejectDialogVisible = ref(false)
const rejectLoading = ref(false)
const rejectFormRef = ref(null)
const rejectForm = reactive({
  reason: ''
})

const rejectRules = {
  reason: [{ required: true, message: '请输入审核不通过的原因', trigger: 'blur' }]
}

// 历史记录对话框相关
const historyDialogVisible = ref(false)
const historyLoading = ref(false)
const historyData = ref([])
const originalStartDate = ref('')

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 远程搜索市场部经理
const remoteSearchDeptManager = async (query) => {
  if (query) {
    try {
      const response = await http.post(
        '/rpc/p_dept_fix_indx_set_s',
        {
          i_request: {
            optionflg: '1',
            srcsys: srcsys.value,
            oacode: oacode.value,
            roleid: roleid.value,
            p_dept_mngr_cd: query,
            p_last_flag: '1',
            p_org_belong: 'latest'
          }
        },
        {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        }
      )

      if (response.data) {
        const uniqueManagers = new Map()
        response.data.forEach(item => {
          if (item.dept_mngr_cd && item.dept_mngr_nam) {
            uniqueManagers.set(item.dept_mngr_cd, {
              value: item.dept_mngr_cd,
              label: item.dept_mngr_nam
            })
          }
        })
        deptManagerOptions.value = Array.from(uniqueManagers.values())
      }
    } catch (error) {
      console.error('搜索市场部经理失败:', error)
    }
  } else {
    deptManagerOptions.value = []
  }
}

// 判断审核按钮是否禁用
const isAuditDisabled = (row) => {
  // 已审核的记录（audt_relt in (1,2)）或没有uuid的记录禁用审核按钮
  return ['1', '2'].includes(row.audt_relt) || !row.uuid
}

// 查询数据
const handleQuery = async () => {
  loading.value = true
  try {
    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_s',
      {
        i_request: {
          optionflg: '1',
          srcsys: srcsys.value,
          oacode: oacode.value,
          roleid: roleid.value,
          p_tect_strt_date: queryForm.tectStrtDate,
          p_tect_end_date: queryForm.tectEndDate,
          p_brh_cd: queryForm.branchCode,
          p_dept_mngr_cd: queryForm.deptMngrCd,
          p_last_flag: '1',
          p_org_belong: 'latest'
        }
      },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data) {
      tableData.value = response.data
      totalCount.value = response.data.length
      // ElMessage.success(`查询成功，共找到 ${response.data.length} 条记录`)
    } else {
      tableData.value = []
      totalCount.value = 0
      ElMessage.info('未找到相关数据')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请检查网络连接')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  try {
    loading.value = true

    // 使用当前查询条件获取所有数据
    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_s',
      {
        i_request: {
          optionflg: '1',
          srcsys: srcsys.value,
          oacode: oacode.value,
          roleid: roleid.value,
          p_tect_strt_date: queryForm.tectStrtDate,
          p_tect_end_date: queryForm.tectEndDate,
          p_brh_cd: queryForm.branchCode,
          p_dept_mngr_cd: queryForm.deptMngrCd,
          p_last_flag: '1',
          p_org_belong: 'latest'
        }
      },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    const exportData = response.data || []

    // 准备导出数据
    const excelData = [
      // 表头
      [
        '生效开始日期', '生效结束日期', '分支机构代码', '分支机构名称', '市场部经理代码', '市场部经理名称',
        '岗位名称', '岗位职级', '保有日均权益上年基数', '净增日均权益上年12月基数', '净增日均权益月目标',
        '日均权益考核权重', '净收入月度目标', '净收入考核权重', '新增有效户月度目标', '新增有效户考核权重',
        '必选指标考核权重', '市场部考核占比', '市场部经理个人考核占比', '创建人', '创建时间',
        '审核人', '审核时间', '审核结果', '审核不通过原因'
      ],
      // 数据行
      ...exportData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.brh_cd || '',
        item.brh_nam || '',
        item.dept_mngr_cd || '',
        item.dept_mngr_nam || '',
        item.post_nam || '',
        item.post_lvl || '',
        item.stay_equi_last_base || '',
        item.nadd_equi_last_base || '',
        item.nadd_equi_tgt || '',
        item.nadd_equi_wght || '',
        item.net_incm_tgt || '',
        item.net_incm_wght || '',
        item.add_val_tgt || '',
        item.add_val_wght || '',
        item.fix_indx_wght || '',
        item.dept_pct || '',
        item.dept_mngr_pct || '',
        item.creator || '',
        item.crt_time || '',
        item.audt_prsn || '',
        item.audt_time || '',
        item.audt_relt === '0' ? '未审核' : item.audt_relt === '1' ? '审核通过' : item.audt_relt === '2' ? '审核不通过' : item.audt_relt,
        item.audt_no_pass_reason || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = Array(25).fill({ wch: 15 })
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '市场部必选指标设置')

    // 生成文件名
    const now = new Date()
    const fileName = `市场部必选指标设置_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${exportData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 处理修改
const handleEdit = (row) => {
  currentRow.value = row
  originalStartDate.value = row.tect_strt_date

  // 填充表单数据
  Object.keys(editForm).forEach(key => {
    if (row[key] !== undefined) {
      editForm[key] = row[key]
    }
  })

  editDialogVisible.value = true
}

// 保存修改
const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    // 验证生效开始日期不能小于原始值
    if (editForm.tect_strt_date < originalStartDate.value) {
      ElMessage.error('生效开始日期不能小于原始值')
      return
    }

    editLoading.value = true

    const requestData = {
      optionflg: editForm.uuid ? '2' : '1', // 有uuid为修改，无uuid为新增
      srcsys: srcsys.value,
      oacode: oacode.value,
      roleid: roleid.value,
      tect_strt_date: editForm.tect_strt_date,
      tect_end_date: editForm.tect_end_date,
      dept_mngr_cd: editForm.dept_mngr_cd,
      stay_equi_last_base: editForm.stay_equi_last_base,
      nadd_equi_last_base: editForm.nadd_equi_last_base,
      nadd_equi_tgt: editForm.nadd_equi_tgt,
      nadd_equi_wght: editForm.nadd_equi_wght,
      net_incm_tgt: editForm.net_incm_tgt,
      net_incm_wght: editForm.net_incm_wght,
      add_val_tgt: editForm.add_val_tgt,
      add_val_wght: editForm.add_val_wght,
      fix_indx_wght: editForm.fix_indx_wght,
      dept_pct: editForm.dept_pct,
      dept_mngr_pct: editForm.dept_mngr_pct,
      uuid: editForm.uuid || ''
    }

    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_e',
      { i_request: requestData },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data && response.data.o_status === 0) {
      ElMessage.success(response.data.o_msg || '保存成功')
      editDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查网络连接')
  } finally {
    editLoading.value = false
  }
}

// 处理审核通过
const handleApprove = (row) => {
  currentRow.value = row
  approveDialogVisible.value = true
}

// 确认审核通过
const handleApproveConfirm = async () => {
  if (!currentRow.value) return

  approveLoading.value = true

  try {
    const requestData = {
      optionflg: '4',
      srcsys: srcsys.value,
      oacode: oacode.value,
      roleid: roleid.value,
      audt_relt: '1', // 审核通过
      uuid: currentRow.value.uuid
    }

    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_e',
      { i_request: requestData },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data && response.data.o_status === 0) {
      ElMessage.success(response.data.o_msg || '审核通过成功')
      approveDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || '审核通过失败')
    }
  } catch (error) {
    console.error('审核通过失败:', error)
    ElMessage.error('审核通过失败，请检查网络连接')
  } finally {
    approveLoading.value = false
  }
}

// 处理审核不通过
const handleReject = (row) => {
  currentRow.value = row
  rejectForm.reason = ''
  rejectDialogVisible.value = true
}

// 确认审核不通过
const handleRejectConfirm = async () => {
  if (!rejectFormRef.value) return

  try {
    await rejectFormRef.value.validate()

    rejectLoading.value = true

    const requestData = {
      optionflg: '4',
      srcsys: srcsys.value,
      oacode: oacode.value,
      roleid: roleid.value,
      audt_relt: '2', // 审核不通过
      audt_no_pass_reason: rejectForm.reason,
      uuid: currentRow.value.uuid
    }

    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_e',
      { i_request: requestData },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data && response.data.o_status === 0) {
      ElMessage.success(response.data.o_msg || '审核不通过成功')
      rejectDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || '审核不通过失败')
    }
  } catch (error) {
    console.error('审核不通过失败:', error)
    ElMessage.error('审核不通过失败，请检查网络连接')
  } finally {
    rejectLoading.value = false
  }
}

// 处理历史记录
const handleHistory = async (row) => {
  currentRow.value = row
  historyLoading.value = true
  historyDialogVisible.value = true

  try {
    const response = await http.post(
      '/rpc/p_dept_fix_indx_set_s',
      {
        i_request: {
          optionflg: '1',
          srcsys: srcsys.value,
          oacode: oacode.value,
          roleid: roleid.value,
          p_dept_mngr_cd: row.dept_mngr_cd,
          p_org_belong: 'latest'
          // p_last_flag 不给值，查询所有历史记录
        }
      },
      {
        headers: {
          'Content-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data) {
      historyData.value = response.data
    } else {
      historyData.value = []
      ElMessage.info('未找到历史记录')
    }
  } catch (error) {
    console.error('查询历史记录失败:', error)
    ElMessage.error('查询历史记录失败，请检查网络连接')
    historyData.value = []
  } finally {
    historyLoading.value = false
  }
}

// 页面初始化
onMounted(() => {
  handleQuery()
})
</script>

<style lang="scss" scoped>
/* empty */
</style>
