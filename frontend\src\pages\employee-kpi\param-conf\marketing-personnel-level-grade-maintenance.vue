<template>
  <div class="report-title">
    <h2>营销人员职级档位维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="生效开始日期:">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效结束日期:">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗位职级:">
              <el-select
                v-model="queryForm.postLevel"
                placeholder="请选择岗位职级"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in postLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="职级档位:">
              <el-select
                v-model="queryForm.levelGrade"
                placeholder="请选择职级档位"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in levelGradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="avg_equi" label="日均权益" />
        <el-table-column prop="val_vol" label="价值量" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="lvl_grad" label="职级档位" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <el-button
              type="success"
              size="small"
              @click="handleHistory(scope.row)"
              >历史记录</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                :disabled="!isEdit"
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日均权益下限" prop="avg_equi_lowl">
              <el-input
                v-model.number="formData.avg_equi_lowl"
                type="number"
                placeholder="请输入日均权益下限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日均权益上限" prop="avg_equi_topl">
              <el-input
                v-model.number="formData.avg_equi_topl"
                type="number"
                placeholder="请输入日均权益上限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="价值量下限" prop="val_vol_lowl">
              <el-input
                v-model.number="formData.val_vol_lowl"
                type="number"
                placeholder="请输入价值量下限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价值量上限" prop="val_vol_topl">
              <el-input
                v-model.number="formData.val_vol_topl"
                type="number"
                placeholder="请输入价值量上限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位职级" prop="post_lvl">
              <el-select
                v-model="formData.post_lvl"
                placeholder="请选择岗位职级"
                :disabled="isEdit"
                style="width: 100%"
              >
                <el-option
                  v-for="item in postLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职级档位" prop="lvl_grad">
              <el-select
                v-model="formData.lvl_grad"
                placeholder="请选择职级档位"
                :disabled="isEdit"
                style="width: 100%"
              >
                <el-option
                  v-for="item in levelGradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :disabled="!isFormValid"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="历史记录" width="1000px">
      <el-table :data="historyData" border stripe table-layout="auto">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="avg_equi" label="日均权益" />
        <el-table-column prop="val_vol" label="价值量" />
        <el-table-column prop="post_lvl" label="岗位职级" />
        <el-table-column prop="lvl_grad" label="职级档位" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as XLSX from "xlsx";
import http from "~/http/http.js";

// 查询表单
const queryForm = reactive({
  startDate: "",
  endDate: "",
  postLevel: "",
  levelGrade: "",
});

// 分页
const currentPage = ref(1);
const pageSize = ref(50);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 字典选项
const postLevelOptions = ref([]);
const levelGradeOptions = ref([]);

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacode: params.get("oacode") || "xurr1",
    roleid: params.get("roleid") || "001",
  };
};

const urlParams = getUrlParams();

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const isEdit = ref(false);

// 历史记录对话框
const historyDialogVisible = ref(false);
const historyData = ref([]);

// 表单数据
const formData = reactive({
  tect_strt_date: "",
  tect_end_date: "",
  avg_equi_lowl: null,
  avg_equi_topl: null,
  val_vol_lowl: null,
  val_vol_topl: null,
  post_lvl: "",
  lvl_grad: "",
  uuid: "",
});

// 原始的开始日期（编辑时保存）
const originalStartDate = ref("");

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: "请选择生效开始日期", trigger: "change" },
  ],
  tect_end_date: [
    { required: true, message: "请选择生效结束日期", trigger: "change" },
  ],
  avg_equi_lowl: [
    { required: true, message: "请输入日均权益下限", trigger: "blur" },
    { type: "number", message: "日均权益下限必须是数字", trigger: "blur" },
  ],
  avg_equi_topl: [
    { required: true, message: "请输入日均权益上限", trigger: "blur" },
    { type: "number", message: "日均权益上限必须是数字", trigger: "blur" },
  ],
  val_vol_lowl: [
    { required: true, message: "请输入价值量下限", trigger: "blur" },
    { type: "number", message: "价值量下限必须是数字", trigger: "blur" },
  ],
  val_vol_topl: [
    { required: true, message: "请输入价值量上限", trigger: "blur" },
    { type: "number", message: "价值量上限必须是数字", trigger: "blur" },
  ],
  post_lvl: [
    { required: true, message: "请选择岗位职级", trigger: "change" },
  ],
  lvl_grad: [
    { required: true, message: "请选择职级档位", trigger: "change" },
  ],
};

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return (
    formData.tect_strt_date &&
    formData.tect_end_date &&
    formData.avg_equi_lowl !== null &&
    formData.avg_equi_lowl !== undefined &&
    formData.avg_equi_topl !== null &&
    formData.avg_equi_topl !== undefined &&
    formData.val_vol_lowl !== null &&
    formData.val_vol_lowl !== undefined &&
    formData.val_vol_topl !== null &&
    formData.val_vol_topl !== undefined &&
    formData.post_lvl &&
    formData.lvl_grad
  );
});

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value || !isEdit.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};

// 加载岗位职级字典数据
const loadPostLevelOptions = async () => {
  try {
    const filters = {
      dict_code: "eq.rykh0004",
    };

    const config = {
      params: {
        ...filters,
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    const response = await http.get("/v_dictionary_cfg", {}, config);
    const data = response.data || [];
    postLevelOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  } catch (error) {
    console.error("加载岗位职级字典数据失败:", error);
    ElMessage.error("加载岗位职级字典数据失败");
  }
};

// 加载职级档位字典数据
const loadLevelGradeOptions = async () => {
  try {
    const filters = {
      dict_code: "eq.rykh0005",
    };

    const config = {
      params: {
        ...filters,
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    const response = await http.get("/v_dictionary_cfg", {}, config);
    const data = response.data || [];
    levelGradeOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  } catch (error) {
    console.error("加载职级档位字典数据失败:", error);
    ElMessage.error("加载职级档位字典数据失败");
  }
};

// 初始化数据
onMounted(async () => {
  await Promise.all([loadPostLevelOptions(), loadLevelGradeOptions()]);
  handleQuery();
});

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {};

  // 添加查询条件
  if (queryForm.startDate && queryForm.endDate) {
    // filters.and = `(tect_strt_date.gte.${queryForm.startDate},tect_strt_date.lte.${queryForm.endDate})`;
    filters.and = `(tect_strt_date.lte.${queryForm.endDate},tect_end_date.gte.${queryForm.startDate})`;
  } else if (queryForm.startDate) {
    filters.tect_end_date = `gte.${queryForm.startDate}`;
  } else if (queryForm.endDate) {
    filters.tect_strt_date = `lte.${queryForm.endDate}`;
  }

  if (queryForm.postLevel) {
    // 将选择的item_code转换为对应的item_value进行查询
    const postLevelOption = postLevelOptions.value.find(opt => opt.value === queryForm.postLevel);
    if (postLevelOption) {
      filters.post_lvl = `eq.${postLevelOption.label}`;
    }
  }

  if (queryForm.levelGrade) {
    // 将选择的item_code转换为对应的item_value进行查询
    const levelGradeOption = levelGradeOptions.value.find(opt => opt.value === queryForm.levelGrade);
    if (levelGradeOption) {
      filters.lvl_grad = `eq.${levelGradeOption.label}`;
    }
  }

  // 添加last_flag=1条件
  filters.last_flag = "eq.1";

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value;
  const limit = pageSize.value;

  const config = {
    params: {
      ...filters,
      order: "tect_strt_date.desc,crt_time.desc",
    },
    headers: {
      Accept: "application/json",
      Range: `${offset}-${offset + limit - 1}`,
      "Accept-Profile": "mkt_base",
    },
  };

  http
    .get("/v_mngr_lvl_grad_mtc", {}, config)
    .then((response) => {
      tableData.value = response.data || [];
      totalCount.value = response.total || 0;
    })
    .catch((error) => {
      console.error("API请求失败:", error);
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false;
    });
};

// 新增
const handleAdd = () => {
  dialogTitle.value = "新增营销人员职级档位维护";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 修改
const handleEdit = (row) => {
  dialogTitle.value = "修改营销人员职级档位维护";
  isEdit.value = true;
  resetForm();

  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(formData, {
    tect_strt_date: row.tect_strt_date,
    tect_end_date: row.tect_end_date,
    uuid: row.uuid,
  });

  // 将显示的岗位职级和职级档位名称转换回代码值用于表单编辑
  const postLevelOption = postLevelOptions.value.find(opt => opt.label === row.post_lvl);
  if (postLevelOption) {
    formData.post_lvl = postLevelOption.value;
  }

  const levelGradeOption = levelGradeOptions.value.find(opt => opt.label === row.lvl_grad);
  if (levelGradeOption) {
    formData.lvl_grad = levelGradeOption.value;
  }

  // 从区间字符串中解析数值
  // 日均权益格式: [10-100)
  const avgEquiMatch = row.avg_equi.match(/\[(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)\)/);
  if (avgEquiMatch) {
    formData.avg_equi_lowl = parseFloat(avgEquiMatch[1]);
    formData.avg_equi_topl = parseFloat(avgEquiMatch[2]);
  }

  // 价值量格式: [20-200)
  const valVolMatch = row.val_vol.match(/\[(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)\)/);
  if (valVolMatch) {
    formData.val_vol_lowl = parseFloat(valVolMatch[1]);
    formData.val_vol_topl = parseFloat(valVolMatch[2]);
  }

  dialogVisible.value = true;
};

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该岗位职级和职级档位的所有历史记录
    const filters = {
      post_lvl: `eq.${row.post_lvl}`,
      lvl_grad: `eq.${row.lvl_grad}`,
    };

    const config = {
      params: {
        ...filters,
        order: "tect_strt_date.desc,crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    const response = await http.get("/v_mngr_lvl_grad_mtc", {}, config);
    historyData.value = response.data || [];
    historyDialogVisible.value = true;
  } catch (error) {
    console.error("获取历史记录失败:", error);
    ElMessage.error("获取历史记录失败");
  }
};

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      "此操作将永久删除该记录, 是否继续?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacode,
        uuid: row.uuid,
      },
    };

    const response = await http.post("/rpc/p_mngr_lvl_grad_mtc_e", requestData, {
      headers: {
        "Content-Type": "application/json",
        "Content-Profile": "mkt_base",
      },
    });

    if (response.data && response.data.o_status === 0) {
      ElMessage.success("删除成功");
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(`删除失败: ${response.data?.o_msg || "未知错误"}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    const requestData = {
      i_request: {
        optionflg: isEdit.value ? "2" : "1",
        oacode: urlParams.oacode,
        tect_strt_date: formData.tect_strt_date,
        tect_end_date: formData.tect_end_date,
        post_lvl: formData.post_lvl,
        lvl_grad: formData.lvl_grad,
        avg_equi_topl: formData.avg_equi_topl,
        avg_equi_lowl: formData.avg_equi_lowl,
        val_vol_topl: formData.val_vol_topl,
        val_vol_lowl: formData.val_vol_lowl,
      },
    };

    // 如果是修改，添加uuid
    if (isEdit.value) {
      requestData.i_request.uuid = formData.uuid;
    }

    const response = await http.post("/rpc/p_mngr_lvl_grad_mtc_e", requestData, {
      headers: {
        "Content-Type": "application/json",
        "Content-Profile": "mkt_base",
      },
    });

    if (response.data && response.data.o_status === 0) {
      ElMessage.success(isEdit.value ? "修改成功" : "新增成功");
      dialogVisible.value = false;
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(
        `${isEdit.value ? "修改" : "新增"}失败: ${
          response.data?.o_msg || "未知错误"
        }`
      );
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    tect_strt_date: "",
    tect_end_date: "",
    avg_equi_lowl: null,
    avg_equi_topl: null,
    val_vol_lowl: null,
    val_vol_topl: null,
    post_lvl: "",
    lvl_grad: "",
    uuid: "",
  });
  originalStartDate.value = "";
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 对话框关闭
const handleDialogClose = () => {
  resetForm();
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    // 构建查询参数，获取所有数据（不分页）
    const filters = {};

    // 添加查询条件
  if (queryForm.startDate && queryForm.endDate) {
    // filters.and = `(tect_strt_date.gte.${queryForm.startDate},tect_strt_date.lte.${queryForm.endDate})`;
    filters.and = `(tect_strt_date.lte.${queryForm.endDate},tect_end_date.gte.${queryForm.startDate})`;
  } else if (queryForm.startDate) {
    filters.tect_end_date = `gte.${queryForm.startDate}`;
  } else if (queryForm.endDate) {
    filters.tect_strt_date = `lte.${queryForm.endDate}`;
  }

    if (queryForm.postLevel) {
      const postLevelOption = postLevelOptions.value.find(opt => opt.value === queryForm.postLevel);
      if (postLevelOption) {
        filters.post_lvl = `eq.${postLevelOption.label}`;
      }
    }

    if (queryForm.levelGrade) {
      const levelGradeOption = levelGradeOptions.value.find(opt => opt.value === queryForm.levelGrade);
      if (levelGradeOption) {
        filters.lvl_grad = `eq.${levelGradeOption.label}`;
      }
    }

    // 添加last_flag=1条件
    filters.last_flag = "eq.1";

    const config = {
      params: {
        ...filters,
        order: "tect_strt_date.desc,crt_time.desc",
      },
      headers: {
        Accept: "application/json",
        "Accept-Profile": "mkt_base",
      },
    };

    // 获取所有数据
    const response = await http.get("/v_mngr_lvl_grad_mtc", {}, config);
    const allData = response.data || [];

    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "生效开始日期",
        "生效结束日期",
        "日均权益",
        "价值量",
        "岗位职级",
        "职级档位",
        "创建时间",
        "更新时间",
      ],
      // 数据行
      ...allData.map((item) => [
        item.tect_strt_date || "",
        item.tect_end_date || "",
        item.avg_equi || "",
        item.val_vol || "",
        item.post_lvl || "",
        item.lvl_grad || "",
        item.crt_time || "",
        item.upd_time || "",
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 日均权益
      { wch: 20 }, // 价值量
      { wch: 15 }, // 岗位职级
      { wch: 15 }, // 职级档位
      { wch: 20 }, // 创建时间
      { wch: 20 }, // 更新时间
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "营销人员职级档位维护");

    // 生成文件名
    const now = new Date();
    const fileName = `营销人员职级档位维护${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};
</script>

<style lang="scss" scoped>
/* empty */
</style>
