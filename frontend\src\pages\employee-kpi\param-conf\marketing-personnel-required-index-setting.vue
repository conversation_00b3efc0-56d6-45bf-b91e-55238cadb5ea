<template>
  <div class="report-title">
    <h2>营销人员必选指标设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务人员">
              <el-select
                v-model="queryForm.busiPrsnCd"
                filterable
                remote
                reserve-keyword
                placeholder="请输入业务人员代码或名称进行搜索"
                :remote-method="remoteSearchBusinessPerson"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in businessPersonOptions"
                  :key="item.value"
                  :label="`${item.value} - ${item.label}`"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效开始日期">
              <el-date-picker
                v-model="queryForm.tectStrtDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效结束日期">
              <el-date-picker
                v-model="queryForm.tectEndDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期"  />
        <el-table-column prop="tect_end_date" label="生效结束日期"  />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称"  />
        <el-table-column prop="busi_prsn_cd" label="业务人员代码"  />
        <el-table-column prop="busi_prsn_nam" label="业务人员名称" />
        <el-table-column prop="post_nam" label="岗位名称"  />
        <el-table-column prop="post_lvl" label="岗位职级"  />
        <el-table-column prop="stay_equi_last_base" label="保有日均权益上年基数" />
        <el-table-column prop="nadd_equi_last_base" label="净增日均权益上年12月基数"/>
        <el-table-column prop="nadd_equi_tgt" label="净增日均权益月目标" />
        <el-table-column prop="nadd_equi_wght" label="日均权益考核权重" />
        <el-table-column prop="net_incm_tgt" label="净收入月目标"  />
        <el-table-column prop="net_incm_wght" label="净收入考核权重"  />
        <el-table-column prop="add_val_tgt" label="新增有效户月目标" />
        <el-table-column prop="add_val_wght" label="新增有效户考核权重" />
        <el-table-column prop="fix_indx_wght" label="必选指标考核权重" />
        <el-table-column prop="zx_pct" label="直销考核占比" />
        <el-table-column prop="ib_pct" label="IB融合考核占比" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_prsn" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column label="审核结果">
          <template #default="scope">
            {{ formatAuditResult(scope.row.audt_relt) }}
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_reason" label="审核不通过原因" />
        <el-table-column label="操作" width="320">
          <template #default="scope">
            <template v-if="urlParams.srcsys === '1'">
              <el-button
                type="success"
                size="small"
                :disabled="isAudited(scope.row.audt_relt) || !scope.row.uuid"
                @click="handleApprove(scope.row)"
                >审核通过</el-button
              >
              <el-button
                type="warning"
                size="small"
                :disabled="isAudited(scope.row.audt_relt) || !scope.row.uuid"
                @click="handleReject(scope.row)"
                >审核不通过</el-button
              >
              <el-button
                type="info"
                size="small"
                @click="handleHistory(scope.row)"
                >历史记录</el-button
              >
            </template>
            <template v-else>
              <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >修改</el-button
              >
              <el-button
                type="info"
                size="small"
                @click="handleHistory(scope.row)"
                >历史记录</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 修改对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改营销人员必选指标设置"
      width="800px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editFormData"
        :rules="editFormRules"
        label-width="150px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-tooltip
                content="修改模式下，开始日期不能早于原值"
                placement="top"
              >
                <el-date-picker
                  v-model="editFormData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="editFormData.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务人员代码" prop="busi_prsn_cd">
              <el-input
                v-model="editFormData.busi_prsn_cd"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保有日均权益上年基数" prop="stay_equi_last_base">
              <el-input-number
                v-model="editFormData.stay_equi_last_base"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="净增日均权益上年12月基数" prop="nadd_equi_last_base">
              <el-input-number
                v-model="editFormData.nadd_equi_last_base"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净增日均权益月目标" prop="nadd_equi_tgt">
              <el-input-number
                v-model="editFormData.nadd_equi_tgt"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日均权益考核权重" prop="nadd_equi_wght">
              <el-input-number
                v-model="editFormData.nadd_equi_wght"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净收入月目标" prop="net_incm_tgt">
              <el-input-number
                v-model="editFormData.net_incm_tgt"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="净收入考核权重" prop="net_incm_wght">
              <el-input-number
                v-model="editFormData.net_incm_wght"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新增有效户月目标" prop="add_val_tgt">
              <el-input-number
                v-model="editFormData.add_val_tgt"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="新增有效户考核权重" prop="add_val_wght">
              <el-input-number
                v-model="editFormData.add_val_wght"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="必选指标考核权重" prop="fix_indx_wght">
              <el-input-number
                v-model="editFormData.fix_indx_wght"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="直销考核占比" prop="zx_pct">
              <el-input-number
                v-model="editFormData.zx_pct"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IB融合考核占比" prop="ib_pct">
              <el-input-number
                v-model="editFormData.ib_pct"
                :precision="4"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEditSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核不通过对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核不通过"
      width="500px"
    >
      <el-form
        ref="rejectFormRef"
        :model="rejectFormData"
        :rules="rejectFormRules"
        label-width="120px"
      >
        <el-form-item label="不通过原因" prop="audt_no_pass_reason">
          <el-input
            v-model="rejectFormData.audt_no_pass_reason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核不通过原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRejectSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="查看历史记录" width="1200px">
      <el-table 
        :data="historyData"
        border
        stripe
        table-layout="auto">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期"  />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="busi_prsn_cd" label="业务人员代码"  />
        <el-table-column prop="busi_prsn_nam" label="业务人员名称"  />
        <el-table-column prop="post_nam" label="岗位名称"  />
        <el-table-column prop="post_lvl" label="岗位职级"  />
        <el-table-column prop="stay_equi_last_base" label="保有日均权益上年基数"  />
        <el-table-column prop="nadd_equi_last_base" label="净增日均权益上年12月基数"  />
        <el-table-column prop="nadd_equi_tgt" label="净增日均权益月目标"  />
        <el-table-column prop="nadd_equi_wght" label="日均权益考核权重"  />
        <el-table-column prop="net_incm_tgt" label="净收入月目标"  />
        <el-table-column prop="net_incm_wght" label="净收入考核权重"  />
        <el-table-column prop="add_val_tgt" label="新增有效户月目标"  />
        <el-table-column prop="add_val_wght" label="新增有效户考核权重" />
        <el-table-column prop="fix_indx_wght" label="必选指标考核权重" />
        <el-table-column prop="zx_pct" label="直销考核占比"  />
        <el-table-column prop="ib_pct" label="IB融合考核占比" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="audt_prsn" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间" />
        <el-table-column label="审核结果">
          <template #default="scope">
            {{ formatAuditResult(scope.row.audt_relt) }}
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_reason" label="审核不通过原因" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as XLSX from "xlsx";
import http from "~/http/http.js";

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

// 查询表单
const queryForm = reactive({
  branchCode: "",
  busiPrsnCd: [],
  tectStrtDate: "",
  tectEndDate: "",
});

// 分页
const currentPage = ref(1);
const pageSize = ref(50);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 存储所有数据的变量（用于前端分页）
const allData = ref([]);

// 业务人员选项
const businessPersonOptions = ref([]);

// 对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref();
const rejectDialogVisible = ref(false);
const rejectFormRef = ref();
const historyDialogVisible = ref(false);
const historyData = ref([]);

// 修改表单数据
const editFormData = reactive({
  tect_strt_date: "",
  tect_end_date: "",
  busi_prsn_cd: "",
  stay_equi_last_base: 0,
  nadd_equi_last_base: 0,
  nadd_equi_tgt: 0,
  nadd_equi_wght: 0,
  net_incm_tgt: 0,
  net_incm_wght: 0,
  add_val_tgt: 0,
  add_val_wght: 0,
  fix_indx_wght: 0,
  zx_pct: 0,
  ib_pct: 0,
  uuid: "",
});

// 审核不通过表单数据
const rejectFormData = reactive({
  audt_no_pass_reason: "",
  uuid: "",
});

// 原始的开始日期（编辑时保存）
const originalStartDate = ref("");

// 修改表单验证规则
const editFormRules = {
  tect_strt_date: [
    { required: true, message: "请选择生效开始日期", trigger: "change" },
  ],
  tect_end_date: [
    { required: true, message: "请选择生效结束日期", trigger: "change" },
  ],
};

// 审核不通过表单验证规则
const rejectFormRules = {
  audt_no_pass_reason: [
    { required: true, message: "请输入审核不通过原因", trigger: "blur" },
  ],
};

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacode: params.get("oacode") || "current_user",
    roleid: params.get("roleid") || "001",
    srcsys: params.get("srcsys") || "1",
  };
};

const urlParams = getUrlParams();

// 审核结果转换函数
const formatAuditResult = (audt_relt) => {
  if (audt_relt === '0' || audt_relt === 0) return '未审核';
  if (audt_relt === '1' || audt_relt === 1) return '审核通过';
  if (audt_relt === '2' || audt_relt === 2) return '审核不通过';
  return audt_relt || '';
};

// 判断是否已审核
const isAudited = (audt_relt) => {
  return audt_relt === '1' || audt_relt === '2';
};

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch;
};

// 远程搜索业务人员
const remoteSearchBusinessPerson = async (query) => {
  if (query) {
    try {
      const config = {
        params: {
          or: `(busi_prsn_cd.ilike.*${query}*,busi_prsn_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Content-Profile': 'mkt_base'
        }
      }

      const response = await http.get('/v_zx_mngr_list1', {}, config)
      const data = response.data || []

      businessPersonOptions.value = data.map(item => ({
        value: item.busi_prsn_cd,
        label: item.busi_prsn_nam
      })).sort((a, b) => a.value.localeCompare(b.value))
    } catch (error) {
      console.error('搜索业务人员失败:', error)
    }
  } else {
    loadBusinessPersonOptions()
  }
}

// 加载业务人员选项
const loadBusinessPersonOptions = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_zx_mngr_list1', {}, config)
    const data = response.data || []

    businessPersonOptions.value = data.map(item => ({
      value: item.busi_prsn_cd,
      label: item.busi_prsn_nam
    })).sort((a, b) => a.value.localeCompare(b.value))
  } catch (error) {
    console.error('获取业务人员选项失败:', error)
    businessPersonOptions.value = []
  }
}

// 初始化数据
onMounted(() => {
  loadBusinessPersonOptions();
  handleQuery();
});

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  try {
    const requestData = {
      i_request: {
        srcsys: urlParams.srcsys,
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        p_tect_strt_date: queryForm.tectStrtDate || "",
        p_tect_end_date: queryForm.tectEndDate || "",
        p_brh_cd: queryForm.branchCode || "",
        p_last_flag: "1",
        p_busi_prsn_cd: Array.isArray(queryForm.busiPrsnCd) ? queryForm.busiPrsnCd.join(',') : queryForm.busiPrsnCd || ""
      }
    };

    const response = await http.post(
      "/rpc/p_mngr_fix_indx_set_s",
      requestData,
      {
        headers: {
          "Content-Profile": "mkt_base",
          "Content-Type": "application/json",
        },
      }
    );

    // 存储过程直接返回数组格式
    const rawData = Array.isArray(response.data) ? response.data : [];

    // 存储所有数据
    allData.value = rawData;

    // 前端分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value;
    const endIndex = startIndex + pageSize.value;
    const paginatedData = rawData.slice(startIndex, endIndex);

    tableData.value = paginatedData;
    totalCount.value = rawData.length;

  } catch (error) {
    console.error("API请求失败:", error);
    console.error("错误详情:", error.response?.data || error.message);
    ElMessage.error("获取数据失败: " + (error.response?.data?.message || error.message));
    // 出错时确保数据是空数组
    tableData.value = [];
    totalCount.value = 0;
  } finally {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false;
  }
};

// 修改
const handleEdit = (row) => {
  // 保存原始开始日期（用于限制后续不能往回选）
  originalStartDate.value = row.tect_strt_date;

  // 填充表单数据
  Object.assign(editFormData, {
    tect_strt_date: row.tect_strt_date,
    tect_end_date: row.tect_end_date,
    busi_prsn_cd: row.busi_prsn_cd,
    stay_equi_last_base: row.stay_equi_last_base || 0,
    nadd_equi_last_base: row.nadd_equi_last_base || 0,
    nadd_equi_tgt: row.nadd_equi_tgt || 0,
    nadd_equi_wght: row.nadd_equi_wght || 0,
    net_incm_tgt: row.net_incm_tgt || 0,
    net_incm_wght: row.net_incm_wght || 0,
    add_val_tgt: row.add_val_tgt || 0,
    add_val_wght: row.add_val_wght || 0,
    fix_indx_wght: row.fix_indx_wght || 0,
    zx_pct: row.zx_pct || 0,
    ib_pct: row.ib_pct || 0,
    uuid: row.uuid,
  });

  editDialogVisible.value = true;
};

// 审核通过
const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm("确认审核通过该条记录？", "审核通过确认", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "info",
    });

    const requestData = {
      i_request: {
        optionflg: "4",
        srcsys: urlParams.srcsys,
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        uuid: row.uuid,
        audt_relt: "1",
        audt_no_pass_reason: "",
      },
    };

    const response = await http.post(
      "/rpc/p_mngr_fix_indx_set_e",
      requestData,
      {
        headers: {
          "Content-Profile": "mkt_base",
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data && response.data.o_status === 0) {
      ElMessage.success("审核通过成功");
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || "审核失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("审核通过时发生错误:", error);
      ElMessage.error("审核失败，请检查网络连接");
    }
  }
};

// 审核不通过
const handleReject = (row) => {
  rejectFormData.uuid = row.uuid;
  rejectFormData.audt_no_pass_reason = "";
  rejectDialogVisible.value = true;
};

// 审核不通过提交
const handleRejectSubmit = async () => {
  if (!rejectFormRef.value) return;

  await rejectFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: "4",
            srcsys: urlParams.srcsys,
            oacode: urlParams.oacode,
            roleid: urlParams.roleid,
            uuid: rejectFormData.uuid,
            audt_relt: "2",
            audt_no_pass_reason: rejectFormData.audt_no_pass_reason,
          },
        };

        const response = await http.post(
          "/rpc/p_mngr_fix_indx_set_e",
          requestData,
          {
            headers: {
              "Content-Profile": "mkt_base",
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.o_status === 0) {
          ElMessage.success("审核不通过成功");
          rejectDialogVisible.value = false;
          handleQuery(); // 重新查询数据
        } else {
          ElMessage.error(response.data?.o_msg || "审核失败");
        }
      } catch (error) {
        console.error("审核不通过时发生错误:", error);
        ElMessage.error("审核失败，请检查网络连接");
      }
    }
  });
};

// 查看历史记录
const handleHistory = async (row) => {
  try {
    const requestData = {
      i_request: {
        srcsys: urlParams.srcsys,
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        p_tect_strt_date: "",
        p_tect_end_date: "",
        p_brh_cd: "",
        p_last_flag: "", // 不给值查询所有
        p_busi_prsn_cd: row.busi_prsn_cd
      }
    };

    const response = await http.post(
      "/rpc/p_mngr_fix_indx_set_s",
      requestData,
      {
        headers: {
          "Content-Profile": "mkt_base",
          "Content-Type": "application/json",
        },
      }
    );

    // 存储过程直接返回数组格式
    historyData.value = Array.isArray(response.data) ? response.data : [];
    historyDialogVisible.value = true;
  } catch (error) {
    console.error("获取历史记录时发生错误:", error);
    ElMessage.error("获取历史记录失败，请检查网络连接");
    // 出错时确保数据是空数组
    historyData.value = [];
  }
};

// 修改提交
const handleEditSubmit = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: editFormData.uuid ? "2" : "1",
            srcsys: urlParams.srcsys,
            oacode: urlParams.oacode,
            roleid: urlParams.roleid,
            tect_strt_date: editFormData.tect_strt_date,
            tect_end_date: editFormData.tect_end_date,
            busi_prsn_cd: editFormData.busi_prsn_cd,
            stay_equi_last_base: editFormData.stay_equi_last_base,
            nadd_equi_last_base: editFormData.nadd_equi_last_base,
            nadd_equi_tgt: editFormData.nadd_equi_tgt,
            nadd_equi_wght: editFormData.nadd_equi_wght,
            net_incm_tgt: editFormData.net_incm_tgt,
            net_incm_wght: editFormData.net_incm_wght,
            add_val_tgt: editFormData.add_val_tgt,
            add_val_wght: editFormData.add_val_wght,
            fix_indx_wght: editFormData.fix_indx_wght,
            zx_pct: editFormData.zx_pct,
            ib_pct: editFormData.ib_pct,
          },
        };

        // 如果是编辑，需要添加uuid
        if (editFormData.uuid) {
          requestData.i_request.uuid = editFormData.uuid;
        }

        const response = await http.post(
          "/rpc/p_mngr_fix_indx_set_e",
          requestData,
          {
            headers: {
              "Content-Profile": "mkt_base",
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.o_status === 0) {
          ElMessage.success("保存成功");
          editDialogVisible.value = false;
          handleQuery(); // 重新查询数据
        } else {
          ElMessage.error(response.data?.o_msg || "保存失败");
        }
      } catch (error) {
        console.error("保存数据时发生错误:", error);
        ElMessage.error("保存失败，请检查网络连接");
      }
    }
  });
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    const requestData = {
      i_request: {
        srcsys: urlParams.srcsys,
        oacode: urlParams.oacode,
        roleid: urlParams.roleid,
        p_tect_strt_date: queryForm.tectStrtDate || "",
        p_tect_end_date: queryForm.tectEndDate || "",
        p_brh_cd: queryForm.branchCode || "",
        p_last_flag: "1",
        p_busi_prsn_cd: Array.isArray(queryForm.busiPrsnCd) ? queryForm.busiPrsnCd.join(',') : queryForm.busiPrsnCd || ""
      }
    };

    const response = await http.post(
      "/rpc/p_mngr_fix_indx_set_s",
      requestData,
      {
        headers: {
          "Content-Profile": "mkt_base",
          "Content-Type": "application/json",
        },
      }
    );

    const rawExportData = response.data || [];

    if (rawExportData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "生效开始日期",
        "生效结束日期",
        "分支机构代码",
        "分支机构名称",
        "业务人员代码",
        "业务人员名称",
        "岗位名称",
        "岗位职级",
        "保有日均权益上年基数",
        "净增日均权益上年12月基数",
        "净增日均权益月目标",
        "日均权益考核权重",
        "净收入月目标",
        "净收入考核权重",
        "新增有效户月目标",
        "新增有效户考核权重",
        "必选指标考核权重",
        "直销考核占比",
        "IB融合考核占比",
        "创建人",
        "创建时间",
        "审核人",
        "审核时间",
        "审核结果",
        "审核不通过原因",
      ],
      // 数据行
      ...rawExportData.map((item) => [
        item.tect_strt_date || "",
        item.tect_end_date || "",
        item.brh_cd || "",
        item.brh_nam || "",
        item.busi_prsn_cd || "",
        item.busi_prsn_nam || "",
        item.post_nam || "",
        item.post_lvl || "",
        item.stay_equi_last_base || "",
        item.nadd_equi_last_base || "",
        item.nadd_equi_tgt || "",
        item.nadd_equi_wght || "",
        item.net_incm_tgt || "",
        item.net_incm_wght || "",
        item.add_val_tgt || "",
        item.add_val_wght || "",
        item.fix_indx_wght || "",
        item.zx_pct || "",
        item.ib_pct || "",
        item.creator || "",
        item.crt_time || "",
        item.audt_prsn || "",
        item.audt_time || "",
        formatAuditResult(item.audt_relt) || "",
        item.audt_no_pass_reason || "",
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 业务人员代码
      { wch: 15 }, // 业务人员名称
      { wch: 15 }, // 岗位名称
      { wch: 12 }, // 岗位职级
      { wch: 20 }, // 保有日均权益上年基数
      { wch: 22 }, // 净增日均权益上年12月基数
      { wch: 18 }, // 净增日均权益月目标
      { wch: 18 }, // 日均权益考核权重
      { wch: 15 }, // 净收入月目标
      { wch: 18 }, // 净收入考核权重
      { wch: 18 }, // 新增有效户月目标
      { wch: 20 }, // 新增有效户考核权重
      { wch: 18 }, // 必选指标考核权重
      { wch: 15 }, // 直销考核占比
      { wch: 18 }, // IB融合考核占比
      { wch: 12 }, // 创建人
      { wch: 20 }, // 创建时间
      { wch: 12 }, // 审核人
      { wch: 20 }, // 审核时间
      { wch: 12 }, // 审核结果
      { wch: 20 }, // 审核不通过原因
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "营销人员必选指标设置");

    // 生成文件名
    const now = new Date();
    const fileName = `营销人员必选指标设置_${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${rawExportData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 处理对话框关闭
const handleEditDialogClose = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields();
  }

  Object.assign(editFormData, {
    tect_strt_date: "",
    tect_end_date: "",
    busi_prsn_cd: "",
    stay_equi_last_base: 0,
    nadd_equi_last_base: 0,
    nadd_equi_tgt: 0,
    nadd_equi_wght: 0,
    net_incm_tgt: 0,
    net_incm_wght: 0,
    add_val_tgt: 0,
    add_val_wght: 0,
    fix_indx_wght: 0,
    zx_pct: 0,
    ib_pct: 0,
    uuid: "",
  });
};

// 前端分页处理函数
const updatePagination = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  const paginatedData = allData.value.slice(startIndex, endIndex);
  tableData.value = paginatedData;
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  updatePagination();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  updatePagination();
};

// 限制开始日期不能早于原始日期
const disabledStartDate = (date) => {
  if (!originalStartDate.value) return false;

  // 将原始日期字符串转为 Date 对象（月初）
  const originalDate = new Date(originalStartDate.value + "-01");
  // 当前选择的月份对应的日期（转为月初比较）
  const currentDate = new Date(
    date.getFullYear() +
      "-" +
      (date.getMonth() + 1).toString().padStart(2, "0") +
      "-01"
  );

  // 禁用所有早于原始开始日期的月份
  return currentDate < originalDate;
};

</script>

<style lang="scss" scoped>
/* temp  */
</style>
