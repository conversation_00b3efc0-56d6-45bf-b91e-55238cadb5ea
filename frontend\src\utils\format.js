// 格式化数字
export const formatNumber = (num, decimalPlaces = 4) => {
  if (num === null || num === undefined) return '0'
  
  // 确保decimalPlaces在合理范围内
  const decimals = Math.max(0, Math.min(decimalPlaces, 4))
  
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals
  })
}

export const formatNumberNoDot = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  })
}

export const formatFriendly = (value, decimalPlaces = 2) => {
    if (value === 0) {
        return '0';
    }
    const absValue = Math.abs(value);
    let unit = '';
    let formattedValueStr = value.toString();

    if (absValue >= 100000000) { // 亿 (100,000,000)
        unit = '亿';
        // 使用指定的 decimalPlaces 进行格式化
        formattedValueStr = (value / 100000000).toFixed(decimalPlaces);
    } else if (absValue >= 10000) { // 万 (10,000)
        unit = '万';
        // 使用指定的 decimalPlaces 进行格式化
        formattedValueStr = (value / 10000).toFixed(decimalPlaces);
    } else {
        // 小于万的数值，直接显示，并根据 decimalPlaces 调整精度
        // 如果 decimalPlaces 为 0，则不带小数
        if (decimalPlaces === 0) {
            return Math.round(value).toString();
        }
        // 否则，保留指定小数位数
        return value.toFixed(decimalPlaces);
    }

    // 清理末尾不必要的小数零
    // 例如：如果 decimalPlaces 是 2，将 "1.20" 变成 "1.2"，将 "3.00" 变成 "3"
    if (decimalPlaces > 0) {
        // 移除末尾的全零（例如 .00）
        formattedValueStr = formattedValueStr.replace(new RegExp(`\\.0{${decimalPlaces}}$`), '');
        // 移除末尾的单个零（例如 .20 -> .2）
        formattedValueStr = formattedValueStr.replace(/\.(\d)0$/, '.$1');
    }
    // 如果清理后小数点后面没有数字了，则移除小数点
    if (formattedValueStr.endsWith('.')) {
        formattedValueStr = formattedValueStr.slice(0, -1);
    }

    return formattedValueStr + unit;
}

export const formatUnitFriendly = (value, unitType = 1, decimalPlaces = 2) => {
    if (value === null || value === undefined) return '0';
    if (value === 0) return '0';
    
    let unit = '';
    let formattedValue = value;
    
    if (unitType === 2 && Math.abs(value) >= 100000000) {
        // 按亿格式化
        unit = '亿';
        formattedValue = value / 100000000;
    } else if (unitType === 1 && Math.abs(value) >= 10000) {
        // 按万格式化
        unit = '万';
        formattedValue = value / 10000;
    } else if (unitType === 2 && Math.abs(value) >= 10000) {
        // 即使小于亿，但要求按亿格式化时，仍按万处理
        unit = '万';
        formattedValue = value / 10000;
    }
    // 如果值小于10000但要求按万格式化，或小于100000000但要求按亿格式化，则不加单位
    
    // 根据指定的小数位数格式化，并添加千位分隔符
    if (decimalPlaces === 0) {
        formattedValue = Math.round(formattedValue).toLocaleString('zh-CN');
    } else {
        // 使用 toLocaleString 添加千位分隔符
        formattedValue = formattedValue.toLocaleString('zh-CN', {
            minimumFractionDigits: decimalPlaces,
            maximumFractionDigits: decimalPlaces
        });
    }
    
    // 清理末尾不必要的小数零
    if (decimalPlaces > 0) {
        // 移除末尾的全零（例如 .00）
        formattedValue = formattedValue.replace(new RegExp(`\\.0{${decimalPlaces}}$`), '');
        // 移除末尾的单个零（例如 .20 -> .2）
        formattedValue = formattedValue.replace(/\.(\d)0$/, '.$1');
    }
    
    // 如果清理后小数点后面没有数字了，则移除小数点
    if (formattedValue.endsWith('.')) {
        formattedValue = formattedValue.slice(0, -1);
    }
    
    return formattedValue + unit;
}

// 格式化日期时间
export const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 获取URL参数的公共方法
 * @returns {Object} 包含 oacode, roleid, srcsys, goBack 的对象
 */
export const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || 'system',
    roleid: params.get('roleid') || '0001',
    srcsys: params.get('srcsys') || '1',
    goBack: params.get('goback') || false,
  }
}